<Project Sdk="MSTest.Sdk/3.6.4">

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <LangVersion>latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UseVSTest>true</UseVSTest>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\SaveDataService\SaveDataService.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Microsoft.NET.Test.Sdk" Version="17.13.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="MSTest.Analyzers" Version="3.8.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="MSTest.TestAdapter" Version="3.8.3" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="MSTest.TestFramework" Version="3.8.3" />
  </ItemGroup>

</Project>
