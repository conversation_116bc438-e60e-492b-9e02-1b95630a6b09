﻿using GameServer.GameService;
using NetService;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace SaveDataService.Server.Websocket
{
    class WebsocketClient : INet
    {
        public WebsocketService websocketService { get; set; }


        private WebSocket _websocket;

        public WebSocket websocket
        {
            get
            {
                return _websocket;
            }
            set
            {
                _websocket = value;
                ///初始化客户端的额很多消息
            }
        }



        public void ReceiveAsync(byte[] bytes, CancellationToken cancellationToken)
        {

        }


        private NetEnum state = NetEnum.Init;
        private string host = null;

        private object userdata;
        private CancellationTokenSource tokenSource = new CancellationTokenSource();
        public void SetUserData(object userdata)
        {
            this.userdata = userdata;
        }
        public T GetUserData<T>() where T : class
        {
            return userdata as T;
        }
        /// <summary>
        /// 发送的消息类型默认值:Text
        /// </summary>
        public static WebSocketMessageType sendType = WebSocketMessageType.Text;

        public ulong id = 0;


        public override event Action OnClose;
        public override event Action<byte[]> OnRecv;
        internal MemoryStream recvStream;

        public WebsocketClient()
        {
            new MemoryStream(new byte[ushort.MaxValue]);
        }
        public WebsocketClient(MemoryStream recvStream)
        {
            this.recvStream = recvStream;
        }

        public override async Task<bool> Connect(string ip, int port = 0)
        {
            try
            {
                host = ip;
                var _sock = new ClientWebSocket();
                state = NetEnum.Cnnecting;
                await _sock.ConnectAsync(new Uri(ip), CancellationToken.None);
                state = NetEnum.Connected;
                websocket = _sock;

                await RecvMessage();
                _OnClose();
            }
            catch (Exception e)
            {
                state = NetEnum.Error;
                Console.WriteLine("000007   " + e.StackTrace);
                return false;
            }
            return true;
        }

        public override async Task<bool> DisConnect()
        {
            if (websocket != null && websocket.State == WebSocketState.Open)
            {
                await websocket.CloseAsync(WebSocketCloseStatus.Empty, "", CancellationToken.None);
                state = NetEnum.Disconnetc;
                return true;
            }
            return false;
        }

        public override NetEnum NetState()
        {
            return state;
        }


        public override Task<bool> ReConnect()
        {
            state = NetEnum.ReConnecting;
            return Connect(host);
        }

        public override async Task<bool> SendMessage(byte[] bytes)
        {
            if (websocket != null && websocket.State == WebSocketState.Open)
            {
                await websocket.SendAsync(bytes, sendType, true, CancellationToken.None);
                return true;
            }
            return false;
        }


        internal void _OnClose()
        {
            OnClose();
        }

        internal void _OnRecv(byte[] bytes)
        {
            OnRecv(bytes);
        }


        /// <summary>
        /// 读取服务器过来的消息
        /// </summary>
        /// <param name="cb"></param>
        /// <returns></returns>
        internal async Task RecvMessage(Action<byte[]> cb = null)
        {
            while (websocket.State == WebSocketState.Open)
            {
                try
                {
                    int receiveCount = 0;
                    ValueWebSocketReceiveResult recv;
                    do
                    {


                        var memory = new Memory<byte>(recvStream.GetBuffer(), receiveCount, recvStream.Capacity - receiveCount);
                        recv = await websocket.ReceiveAsync(memory, tokenSource.Token);
                        receiveCount += recv.Count;



                    }


                    while (!recv.EndOfMessage);
                    if (recv.Count == 0)
                        continue;
                    recvStream.SetLength(recv.Count);
                    if (IsAccept)
                    {
                        cb?.Invoke(recvStream.ToArray());
                    }
                    else
                    {
                        try
                        {
                            byte[] bytes = recvStream.ToArray();
                            //Console.WriteLine($"websocketclient收到客户端{id}的数据[]:{Encoding.UTF8.GetString(bytes)}");

                            ClientData data = new ClientData();
                            data.websocketBytes = bytes;
                            data.websocket = websocket;
                            data.clientID = id;
                            websocketService.handClientMessage(data);

                        }
                        catch (Exception e)
                        {
                            websocket.Dispose();
                            Console.WriteLine("000006   " + e.Message);
                            throw;
                        }
                    }
                }
                catch (Exception e)
                {
                    websocket.Dispose();
                    Console.WriteLine("000005   " + e.Message);
                }
            }
            Console.WriteLine($"收到客户端{id}的信息可能断开链接了，当前状态为：" + websocket.State);
            //既然websocket断开了相关资源要做相应清理
            //websocket.CloseOutputAsync(WebSocketCloseStatus.NormalClosure);

        }


    }
}
