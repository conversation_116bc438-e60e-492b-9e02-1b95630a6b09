﻿using Newtonsoft.Json;
using static AISDKWebSocket.HttpManager;

namespace AISdk.Manage
{
	internal class KuaiShuAISDK : AISDKProvider
	{
		private string url = "https://api.deepseek.com/chat/completions";
		private string key = "Bearer sk-b76ce5226bf94b12a690b54f59936f46";
		public string Model;
		public KuaiShuAISDK(string userMode)
		{
			this.Model = userMode;
		}

		public void SendTextToAIModel(List<string> text, Action<string, bool> callback)
		{
			List<Message> messages = new List<Message>();
			for (int i = 0; i < text.Count; i++)
			{
				if (i % 2 != 0)
				{
					messages.Add(new Message { role = "assistant", content = text[i] });
				}
				else
				{
					messages.Add(new Message { role = "user", content = text[i] });
				}
			}
			if (string.IsNullOrEmpty(Model))
			{
				Model = "DeepSeek-V2.5";
			}
			var data = new KuaiShuData(Model, "user", messages, true);

			Instance.HttpsWeb(url, key, data, (backText, bol) =>
			{
				// UnityEngine.Debug.Log(backText);
				if (!bol)
				{
					backText = this.Model + ": " + backText;
				}
				callback(backText, bol);
			});
		}

        //根据输入的图像内容、视频内容和自然语言指令完成任务
        public void SendImageOrVideoToAiModle(string videoBase64Str, string requestDescrib, Action<string, bool> callback)
        {
            if (string.IsNullOrEmpty(Model))
            {
                Model = "DeepSeek-V2.5";
            }
            string backText = Model + ": " + "暂不支持分析视频，请切换至智谱大模型";
            callback(backText, false);
        }
    }
}
