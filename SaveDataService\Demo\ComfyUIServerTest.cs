using System;
using System.Threading.Tasks;
using SaveDataService.Manage;

namespace SaveDataService.Demo
{
    /// <summary>
    /// ComfyUI服务器管理功能测试类
    /// </summary>
    public class ComfyUIServerTest
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("=== ComfyUI服务器管理功能测试 ===\n");

            await TestAddServer();
            await TestDuplicateServer();
            await TestGetServers();
            await TestServerConnectivity();
            await TestDeleteServer();

            Console.WriteLine("=== 测试完成 ===\n");
        }

        /// <summary>
        /// 测试添加服务器
        /// </summary>
        private static async Task TestAddServer()
        {
            Console.WriteLine("1. 测试添加ComfyUI服务器...");

            var result = await ComfyUIServerManager.AddServerAsync(
                "测试服务器1", 
                "127.0.0.1", 
                8188, 
                "这是一个测试服务器"
            );

            Console.WriteLine($"   结果: {(result.Success ? "成功" : "失败")}");
            Console.WriteLine($"   消息: {result.Message}");
            if (result.ConnectivityInfo != null)
            {
                Console.WriteLine($"   连通性: {(result.ConnectivityInfo.IsOnline ? "在线" : "离线")}");
                Console.WriteLine($"   响应时间: {result.ConnectivityInfo.ResponseTime}ms");
                Console.WriteLine($"   错误信息: {result.ConnectivityInfo.ErrorMessage}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 测试重复服务器检查
        /// </summary>
        private static async Task TestDuplicateServer()
        {
            Console.WriteLine("2. 测试重复服务器检查...");

            // 尝试添加相同IP和端口的服务器
            var result = await ComfyUIServerManager.AddServerAsync(
                "重复服务器", 
                "127.0.0.1", 
                8188, 
                "这应该被拒绝"
            );

            Console.WriteLine($"   结果: {(result.Success ? "成功" : "失败")} (应该失败)");
            Console.WriteLine($"   消息: {result.Message}");
            Console.WriteLine($"   错误代码: {result.ErrorCode}");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试获取服务器列表
        /// </summary>
        private static async Task TestGetServers()
        {
            Console.WriteLine("3. 测试获取服务器列表...");

            var result = await ComfyUIServerManager.GetAllServersAsync();

            Console.WriteLine($"   结果: {(result.Success ? "成功" : "失败")}");
            Console.WriteLine($"   消息: {result.Message}");

            if (result.Success && result.Data is System.Collections.Generic.List<ComfyUIServerConfig> servers)
            {
                Console.WriteLine($"   服务器数量: {servers.Count}");
                foreach (var server in servers)
                {
                    Console.WriteLine($"   - {server.serverName}: {server.GetFullAddress()} (状态: {GetStatusText(server.status)})");
                }
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 测试服务器连通性检查
        /// </summary>
        private static async Task TestServerConnectivity()
        {
            Console.WriteLine("4. 测试服务器连通性检查...");

            var testServer = new ComfyUIServerConfig
            {
                ip = "127.0.0.1",
                port = 8188
            };

            var result = await ComfyUIServerManager.CheckServerConnectivity(testServer);

            Console.WriteLine($"   服务器地址: {result.ServerAddress}");
            Console.WriteLine($"   连通性: {(result.IsOnline ? "在线" : "离线")}");
            Console.WriteLine($"   响应时间: {result.ResponseTime}ms");
            Console.WriteLine($"   错误信息: {result.ErrorMessage}");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试删除服务器
        /// </summary>
        private static async Task TestDeleteServer()
        {
            Console.WriteLine("5. 测试删除服务器...");

            // 首先获取服务器列表
            var getResult = await ComfyUIServerManager.GetAllServersAsync();
            if (getResult.Success && getResult.Data is System.Collections.Generic.List<ComfyUIServerConfig> servers && servers.Count > 0)
            {
                var serverToDelete = servers[0];
                Console.WriteLine($"   准备删除服务器: {serverToDelete.serverName} ({serverToDelete.GetFullAddress()})");

                var deleteResult = await ComfyUIServerManager.DeleteServerAsync(serverToDelete.dbID);
                Console.WriteLine($"   删除结果: {(deleteResult.Success ? "成功" : "失败")}");
                Console.WriteLine($"   消息: {deleteResult.Message}");
            }
            else
            {
                Console.WriteLine("   没有可删除的服务器");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        private static string GetStatusText(byte status)
        {
            return status switch
            {
                0 => "离线",
                1 => "在线",
                2 => "维护",
                _ => "未知"
            };
        }

        /// <summary>
        /// 测试输入验证
        /// </summary>
        public static async Task TestInputValidation()
        {
            Console.WriteLine("=== 输入验证测试 ===\n");

            // 测试空名称
            var result1 = await ComfyUIServerManager.AddServerAsync("", "127.0.0.1", 8188);
            Console.WriteLine($"空名称测试: {result1.Message}");

            // 测试空IP
            var result2 = await ComfyUIServerManager.AddServerAsync("测试", "", 8188);
            Console.WriteLine($"空IP测试: {result2.Message}");

            // 测试无效端口
            var result3 = await ComfyUIServerManager.AddServerAsync("测试", "127.0.0.1", 0);
            Console.WriteLine($"无效端口测试: {result3.Message}");

            // 测试无效IP格式
            var result4 = await ComfyUIServerManager.AddServerAsync("测试", "invalid-ip", 8188);
            Console.WriteLine($"无效IP格式测试: {result4.Message}");

            Console.WriteLine();
        }
    }
}
