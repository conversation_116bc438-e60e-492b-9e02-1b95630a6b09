﻿
using GameServer.GameService;

using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IO;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.WebSockets;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;


namespace SaveDataService.Server.Websocket
{
    public delegate Task deleProcessHttp(HttpContext context);
    public class ActionController : IController
    {
        public ActionController(deleProcessHttp action)
        {
            this.action = action;
        }
        public async Task ProcessAsync(HttpContext context)
        {
            await action(context);
        }
        deleProcessHttp action;
    }

    public interface IController
    {
        Task ProcessAsync(HttpContext context);
    }
    class WebsocketService : BaseServer
    {
        private IWebHost host;

        public Func<HttpContext, Task> OnHttp404;

        public Func<HttpContext, Task<bool>> OnRequest;

        /// <summary>
        /// 服务器可以被远程调用的方法
        /// </summary>
        public Dictionary<string, RemoteProcedureCall> remoteProcedureCalls = new Dictionary<string, RemoteProcedureCall>();

        public static ConcurrentDictionary<string, bool> isPayDone = new ConcurrentDictionary<string, bool>();
        /// <summary>
        /// Bing团队发布了Microsoft.IO.RecyclableMemoryStream是.NET MemoryStream替代，优化提供了对象池以提高应用的性能，在以下几个方面优化：
        /// 1.通过池化缓冲消除了大型对象的Heap分配
        /// 2.更少的GC, 每次GC导致的暂停更短
        /// 3.通过固定大小的对象池避免了内存泄漏
        /// 4.避免内存碎片
        /// 5.提供超棒的调试性
        /// 6.提供性能跟踪衡量
        /// </summary>
        public RecyclableMemoryStreamManager MemoryStreamManager = new RecyclableMemoryStreamManager();


        /// <summary>
        /// 所有从网络过来的数据都会被存入到这个队列当中 这个是一个线程安全的队先进先出队列
        /// </summary>
        ConcurrentQueue<WebsocketClientData> websocketClientDatas = new ConcurrentQueue<WebsocketClientData>();

        public string requestPath = "";

        /// <summary>
        /// 每次来一个链接就+1
        /// </summary>  
        public ulong connIdIndex = 0;


        /// <summary>
        /// 线程安全的字典
        /// </summary>
        public ConcurrentDictionary<ulong, WebsocketClient> websocketClients = new ConcurrentDictionary<ulong, WebsocketClient>();


        public WebsocketService()
        {
            onHttpEvents = new ConcurrentDictionary<string, IController>();
        }
        public ConcurrentDictionary<string, IController> onHttpEvents;
        private async Task ProcessAsync(HttpContext context)
        {
            try
            {
                var path = context.Request.Path.Value;
                if (onHttpEvents.TryGetValue(path.ToLower(), out IController controller))
                {
                    await controller.ProcessAsync(context);
                }
                else
                {
                    await OnWebScoket(context);
                }
            }
            catch
            {

            }
        }
        public void SetHttpAction(string path, deleProcessHttp httpaction)
        {
            onHttpEvents[path] = new ActionController(httpaction);
        }
        deleProcessHttp onHttp404;
        public void SetFailAction(deleProcessHttp httpaction)
        {
            onHttp404 = httpaction;
        }
        private async Task OnWebScoket(HttpContext context)
        {
            try
            {
                var headers = context.Response.Headers;
                headers.Add("Access-Control-Allow-Origin", "*");
                headers.Add("Access-Control-Allow-Headers", "x-requested-with,content-type");
                headers.Add("Access-Control-Allow-Methods", "POST,GET");
                context.Response.ContentType = "text/plain; charset=utf-8";
                if (context.Request.Method == "OPTIONS")
                    return;
                string path = context.Request.Path.Value;
                if (OnRequest != null)
                {
                    bool isHandle = await OnRequest(context);
                    if (isHandle)
                        return;
                }

                if (OnHttp404 != null)
                {
                    await OnHttp404(context);
                }
                else
                {

                    // await ctx.Response.WriteAsync($"nofind page: {path}");
                }
            }
            catch (Exception e)
            {
                Console.WriteLine("000004   " + $"{e.Message}" +
                    $"{e.StackTrace}");

                await context.Response.WriteAsync($"{e.Message}" +
                   $"{e.StackTrace}");
            }
        }

        /// <summary>
        /// 传入加密密钥则启动加密的webscoekt也就是访问的时候地址是wss而不是ws
        /// </summary>
        /// <param name="port"></param>
        /// <param name="pfxpath"></param>
        /// <param name="password"></param>
        public void Start(int port, string pfxpath = null, string password = null)
        {
            host = new WebHostBuilder().UseKestrel((options) =>
            {
                if (string.IsNullOrEmpty(pfxpath) || string.IsNullOrEmpty(password))
                {
                    options.ListenAnyIP(port, (configure) =>
                    {
                        Console.WriteLine($"listen httpServer :{configure.IPEndPoint.Address}:{configure.IPEndPoint.Port}");
                    });
                }
                else
                {
                    options.Listen(IPAddress.Any, port, configure =>
                    {
                        configure.UseHttps(pfxpath, password);
                        Console.WriteLine($"listen httpServer :{configure.IPEndPoint.Address}:{configure.IPEndPoint.Port}");
                    });
                }
            }).Configure(app =>
            {
                //开启websocket
                app.UseWebSockets();
                //开启压缩
                app.UseResponseCompression();
                app.Run(ProcessAsync);
            }).ConfigureServices(service =>
            {
                service.AddResponseCompression(options =>
                {

                    options.EnableForHttps = true;
                    options.Providers.Add<GzipCompressionProvider>();
                    options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(new[] { "application/json" });
                });

                service.Configure<GzipCompressionProviderOptions>(options =>
                {
                    options.Level = CompressionLevel.Fastest;

                });
            }).Build();
            try
            {
                host.Start();
                ConnectService();
            }
            catch (Exception ex)
            {
                Console.WriteLine("开启websocket服务器失败,失败原因为：" + ex.Message.ToString());
            }

        }

        public override void init()
        {
            throw new NotImplementedException();
        }

        public override void ConnectService()
        {
            OnRequest = async (ctx) =>
            {
                //Console.WriteLine("客户端链接的地址是：" + ctx.Connection.RemoteIpAddress);
                if (ctx.WebSockets.IsWebSocketRequest)
                {
                    return await WebSocketsProcess(ctx);//WSHandle websocket处理
                }
                return await HttpProcess(ctx);///HttpHandle http 处理
            };
        }



        public async Task<bool> HttpProcess(HttpContext context)
        {
            //Console.WriteLine(context.Request.Path);
            //Console.WriteLine(context.Request.ContentType);

            

            ClientData data = new ClientData();
            data.httpContext = context;
            data.ip = context.Connection.RemoteIpAddress?.ToString();
            data.getHttpStringData();
            //TODO:https协议过来如何解决问题。
            handClientMessage(data);
            //Console.WriteLine("000003   " + Process.GetCurrentProcess().TotalProcessorTime);


            ///将前端发送过来的消息原样发送过去
            //try
            //{
            //    await context.Response.WriteAsync(text);
            //}
            //catch (Exception e)
            //{
            //    Console.WriteLine(e.Message);
            //}

            return true;
        }
        //private Dictionary<string,payPlatform> payPlatformData;

        public void handClientMessage(ClientData data)
        {
            RemoteProcedureCall requeobj = null;



            //RemoteProcedureCall rc = new RemoteProcedureCall();
            //rc.className = "PayOrderHandler";
            //rc.functionName = "wxToToken";
            //rc.args = new List<string>();
            //rc.args.Add("wxToToken");
            //string json2 = JsonSerializer.Serialize<RemoteProcedureCall>(rc);
            try
            {
                //ClientRequestManager.InvokeMethod(data);
                return;

            }
            catch (Exception e)
            {
                Console.WriteLine("error: " + e.Message + "\n" + e.StackTrace);
                return;
            }
            try
            {


                if (!string.IsNullOrEmpty(data.httpContextString))
                {
                    requeobj = JsonSerializer.Deserialize<RemoteProcedureCall>(data.httpContextString);
                }
                else
                {
                    //MemoryStream ms = new MemoryStream(data.websocketBytes);
                    //BinaryReader br = new BinaryReader(ms);
                    //float webscoektMessageVerstion = br.ReadSingle();
                    //float messageType = br.ReadSingle();
                    //byte[] by = br.ReadBytes(data.websocketBytes.Length);
                    //switch (messageType)
                    //{
                    //    case 1:

                    //        string json = Encoding.UTF8.GetString(by);
                    //        requeobj = JsonSerializer.Deserialize<RemoteProcedureCall>(json);
                    //        break;
                    //    default:
                    //        break;
                    //}
                    string json = Encoding.UTF8.GetString(data.websocketBytes);
                    requeobj = JsonSerializer.Deserialize<RemoteProcedureCall>(json);

                }


            }
            catch (Exception ex)
            {
                //Console.WriteLine("前端发送的协议有问题:" + ex.ToString());
            }
            callFunction(requeobj, data);
        }
        public static ConcurrentDictionary<Type, object> classMap = new ConcurrentDictionary<Type, object>();

        public void callFunction(RemoteProcedureCall requeobj,ClientData data)
        {





            try
            {
                if (requeobj == null)
                {
                    Console.WriteLine("没有回调程序");
                    //data.sendLog("当前版本："+ BaseGameService.Version.version + Test.Logs.log);
                    return;
                }
                //取出服务器希望调用的方法
                string functionName = requeobj.className + "." + requeobj.functionName;

                //确定这个方法是服务器允许调用列表里面的
                if (remoteProcedureCalls.ContainsKey(functionName))
                {
                    //将服务器的类和方法初始化出来一个对象
                    RemoteProcedureCall remoteProcedureCall = remoteProcedureCalls[functionName];
                    Type type = remoteProcedureCall.GetType();




                    ///从客户端传输过来的参数获取出所有的参数,有可能方法没有参数
                    object[] functionParams;
                    dynamic[] values = null;
                    if (remoteProcedureCall.argsType.Count != 0)
                    {
                        values = requeobj.args.ToArray();
                        functionParams = new object[remoteProcedureCall.argsType.Count];
                    }
                    else
                    {
                        functionParams = null;
                    }

                    ///前端传输过来的参数数量和客户端的参数数量必须是一致的才会进行调用
                    if (values != null && values.Length == remoteProcedureCall.argsType.Count-1 || values == null)
                    {

                        for (int i = 0; i < remoteProcedureCall.argsType.Count-1; i++)
                        {
                            ///由于提前存储起来了远程过程调用方法的参数变量类型所以这里直接将客户端的参数string转为服务器相应的类型
                            //functionParams[i] = Convert.ChangeType( values[i], remoteProcedureCall.argsType[i]);
                            functionParams[i] = values[i];
                        }
                        functionParams[functionParams.Length-1] = data;
                        //通过反射调用服务器相应的方法（这些方法存在字典中是可以防止被客户端调用到服务器其他不允许调用的方法里面）
                        object instance;
                        if (!classMap.ContainsKey(remoteProcedureCall.currentType))
                        {
                            instance = Activator.CreateInstance(remoteProcedureCall.currentType);
                            classMap[remoteProcedureCall.currentType] = instance;
                        }
                        else
                        {
                            instance = classMap[remoteProcedureCall.currentType];
                        }
                     
                        MethodInfo methodInfo = remoteProcedureCall.currentType.GetMethod(requeobj.functionName);
                        methodInfo.Invoke(instance, functionParams);
                        //Console.WriteLine("反射调用成功，方法名字为:" + functionName);
                    }
                    else
                    {
                        Console.WriteLine("前端传输过来的参数数量和客户端的参数数量必须是一致的才会进行调用:" + functionName);
                    }
                }
                else
                {
                    Console.WriteLine("客户端调用服务器的方法不存在请检查客户端是否代码写错误或者是客户端恶意攻击。方法名字是：" + functionName + " 客户单的ip是：");
                }
            }
            catch (Exception e)
            {
                Console.WriteLine("000002   "+e.Message);
            }
        }



        public async Task<bool> WebSocketsProcess(HttpContext ctx)
        {

            ///加入path的判断 可以用于特殊比如gm命令的模块
            /// if (requestPath != ctx.Request.Path)

            WebSocket websocket = await ctx.WebSockets.AcceptWebSocketAsync();
            var websocketClient = new WebsocketClient(MemoryStreamManager.GetStream("WebsocketServiceMemoryStream"));
            websocketClient.websocket = websocket;
            websocketClient.id = ++connIdIndex;
            websocketClient.IsAccept = true;
            websocketClients[websocketClient.id] = websocketClient;
            websocketClient.websocketService = this;

            Console.WriteLine($"检测到客户端{websocketClient.id},通过websocket链接过来：" + websocket.SubProtocol);
            await websocketClient.RecvMessage(bytes =>
            {
                //直接吧前端发过来的消息回复给前端
                //websocket.SendAsync(bytes, WebSocketMessageType.Binary, true, CancellationToken.None);
                //Console.WriteLine($"webservice收到客户端{websocketClient.id}的数据:{Encoding.UTF8.GetString(bytes)}");

                ClientData data = new ClientData();
                data.websocket = websocket;
                data.websocketBytes = bytes;
                data.ip = ctx.Connection.RemoteIpAddress?.ToString();
                data.clientID = websocketClient.id;
                handClientMessage(data);

            });

            return true;
        }

  


        public override void DisConnectService()
        {

        }
    }
}
