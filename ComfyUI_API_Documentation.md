# ComfyUI服务器管理API文档

本文档描述了ComfyUI服务器管理功能的API接口，包括添加、查询、删除和检查服务器连通性等功能。

## 功能特性

- ✅ **重复检查**: 自动检查IP和端口组合是否已存在，避免重复添加
- ✅ **连通性检查**: 自动检测服务器是否在线
- ✅ **输入验证**: 验证IP地址格式和端口范围
- ✅ **错误处理**: 详细的错误信息和错误代码
- ✅ **CORS支持**: 支持跨域请求

## API接口

### 1. 添加ComfyUI服务器

**接口地址**: `POST /comfyui/addserver`

**请求参数**:
```json
{
    "serverName": "我的ComfyUI服务器",
    "ip": "127.0.0.1",
    "port": 8188,
    "description": "本地开发服务器"
}
```

**成功响应**:
```json
{
    "Success": true,
    "Message": "ComfyUI服务器 '我的ComfyUI服务器' 添加成功！地址: 127.0.0.1:8188",
    "ErrorCode": "",
    "Data": {
        "dbID": 1,
        "serverName": "我的ComfyUI服务器",
        "ip": "127.0.0.1",
        "port": 8188,
        "description": "本地开发服务器",
        "isEnabled": true,
        "createTime": "2024-01-01T10:00:00",
        "updateTime": "2024-01-01T10:00:00",
        "status": 1
    },
    "ConnectivityInfo": {
        "ServerAddress": "http://127.0.0.1:8188",
        "IsOnline": true,
        "ResponseTime": 150,
        "ErrorMessage": "连接成功"
    }
}
```

**重复服务器错误响应**:
```json
{
    "Success": false,
    "Message": "服务器 127.0.0.1:8188 已存在！现有服务器名称: 我的ComfyUI服务器",
    "ErrorCode": "DUPLICATE_SERVER",
    "Data": null,
    "ConnectivityInfo": null
}
```

### 2. 获取所有ComfyUI服务器

**接口地址**: `POST /comfyui/getservers`

**请求参数**: 空JSON对象 `{}`

**成功响应**:
```json
{
    "Success": true,
    "Message": "获取到 2 个ComfyUI服务器配置",
    "ErrorCode": "",
    "Data": [
        {
            "dbID": 1,
            "serverName": "本地服务器",
            "ip": "127.0.0.1",
            "port": 8188,
            "description": "本地开发环境",
            "isEnabled": true,
            "createTime": "2024-01-01T10:00:00",
            "updateTime": "2024-01-01T10:00:00",
            "status": 1
        },
        {
            "dbID": 2,
            "serverName": "远程服务器",
            "ip": "*************",
            "port": 8188,
            "description": "生产环境",
            "isEnabled": true,
            "createTime": "2024-01-01T11:00:00",
            "updateTime": "2024-01-01T11:00:00",
            "status": 0
        }
    ],
    "ConnectivityInfo": null
}
```

### 3. 删除ComfyUI服务器

**接口地址**: `POST /comfyui/deleteserver`

**请求参数**:
```json
{
    "serverId": 1
}
```

**成功响应**:
```json
{
    "Success": true,
    "Message": "服务器 '本地服务器' (127.0.0.1:8188) 删除成功",
    "ErrorCode": "",
    "Data": null,
    "ConnectivityInfo": null
}
```

**服务器不存在错误响应**:
```json
{
    "Success": false,
    "Message": "未找到ID为 999 的服务器配置",
    "ErrorCode": "SERVER_NOT_FOUND",
    "Data": null,
    "ConnectivityInfo": null
}
```

### 4. 检查服务器连通性

**接口地址**: `POST /comfyui/checkserver`

**请求参数**:
```json
{
    "ip": "127.0.0.1",
    "port": 8188
}
```

**成功响应**:
```json
{
    "Success": true,
    "Message": "连通性检查完成",
    "ErrorCode": "",
    "Data": null,
    "ConnectivityInfo": {
        "ServerAddress": "http://127.0.0.1:8188",
        "IsOnline": true,
        "ResponseTime": 120,
        "ErrorMessage": "连接成功"
    }
}
```

**离线服务器响应**:
```json
{
    "Success": true,
    "Message": "连通性检查完成",
    "ErrorCode": "",
    "Data": null,
    "ConnectivityInfo": {
        "ServerAddress": "http://*************:8188",
        "IsOnline": false,
        "ResponseTime": -1,
        "ErrorMessage": "连接超时"
    }
}
```

## 错误代码说明

| 错误代码 | 说明 |
|---------|------|
| `DUPLICATE_SERVER` | 服务器IP和端口组合已存在 |
| `DUPLICATE_NAME` | 服务器名称已存在 |
| `INVALID_NAME` | 服务器名称为空或无效 |
| `INVALID_IP` | IP地址为空 |
| `INVALID_IP_FORMAT` | IP地址格式不正确 |
| `INVALID_PORT` | 端口号不在有效范围内(1-65535) |
| `SERVER_NOT_FOUND` | 指定的服务器不存在 |
| `INTERNAL_ERROR` | 服务器内部错误 |

## 服务器状态说明

| 状态值 | 说明 |
|-------|------|
| 0 | 离线 |
| 1 | 在线 |
| 2 | 维护中 |

## 使用示例

### JavaScript/Fetch示例

```javascript
// 添加服务器
async function addComfyUIServer() {
    const response = await fetch('http://localhost:7778/comfyui/addserver', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            serverName: '我的ComfyUI服务器',
            ip: '127.0.0.1',
            port: 8188,
            description: '本地开发服务器'
        })
    });
    
    const result = await response.json();
    
    if (result.Success) {
        console.log('服务器添加成功:', result.Message);
        console.log('连通性:', result.ConnectivityInfo);
    } else {
        console.error('添加失败:', result.Message);
        console.error('错误代码:', result.ErrorCode);
    }
}

// 获取服务器列表
async function getComfyUIServers() {
    const response = await fetch('http://localhost:7778/comfyui/getservers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
    
    const result = await response.json();
    
    if (result.Success) {
        console.log('服务器列表:', result.Data);
    } else {
        console.error('获取失败:', result.Message);
    }
}
```

### cURL示例

```bash
# 添加服务器
curl -X POST http://localhost:7778/comfyui/addserver \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "测试服务器",
    "ip": "127.0.0.1",
    "port": 8188,
    "description": "测试用服务器"
  }'

# 获取服务器列表
curl -X POST http://localhost:7778/comfyui/getservers \
  -H "Content-Type: application/json" \
  -d '{}'

# 删除服务器
curl -X POST http://localhost:7778/comfyui/deleteserver \
  -H "Content-Type: application/json" \
  -d '{"serverId": 1}'
```

## 注意事项

1. **重复检查**: 系统会自动检查IP和端口的组合，如果已存在相同的组合，会返回错误信息并拒绝添加。

2. **连通性检查**: 添加服务器时会自动检查连通性，但这不会阻止服务器的添加，只是更新状态信息。

3. **CORS支持**: API支持跨域请求，可以从前端直接调用。

4. **错误处理**: 所有API都会返回详细的错误信息和错误代码，便于调试和处理。

5. **数据持久化**: 所有服务器配置都会保存到数据库中，重启服务后数据不会丢失。
