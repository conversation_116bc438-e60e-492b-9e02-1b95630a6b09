﻿using Azure;
using ExcelDataReader.Log;
using Microsoft.AspNetCore.Http;
using Microsoft.Net.Http.Headers;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using NPOI.OpenXmlFormats.Spreadsheet;
using SaveDataService.Server.Websocket;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Aliyun.OSS.Model.ListMultipartUploadsResult;

namespace SaveDataService.Manage
{
    internal class PostManager
    {
        public static void init()
        {

            var httpserver = new WebsocketService();
            httpserver.SetFailAction(on404);
            //httpserver.SetWebsocketAction("/wsapi", onWebsocketIn);
            //httpserver.SetHttpAction("/userInput", onUpload);
            //前置需求
            httpserver.SetHttpAction("/basetemplate", OnSetBaseTemplate);
            //用户输入生成游戏描述
            httpserver.SetHttpAction("/userinput", onUserInput);
            //在模板的基础上，用户调整的游戏内容
            httpserver.SetHttpAction("/refreshgamebase", onRefreshGamebase);
            ////IGNlink 评测连接
            //httpserver.SetHttpAction("/ignreviewlink", onIGNreviewlink);
            ////游戏简介
            //httpserver.SetHttpAction("/gameintroduce", onGameIntroduce);
            //游戏章节
            httpserver.SetHttpAction("/chapter", onChapter);
            //角色设定 关键字
            httpserver.SetHttpAction("/characterkeys", onCharacterKeys);
            //场景设定 关键字
            httpserver.SetHttpAction("/scencekeys", onScenceKeys);
            //场景背景音乐 
            httpserver.SetHttpAction("/scencemusic", onScenceMusic);
            //场景音效
            httpserver.SetHttpAction("/scenceeffect", onScenceEffect);
            //天空盒子
            httpserver.SetHttpAction("/skybox", onSkybox);

            //策划案
            httpserver.SetHttpAction("/designcases", onDesignCases);

            httpserver.SetHttpAction("/reexceldb", onRefreshExcel);

            // ComfyUI服务器管理接口
            httpserver.SetHttpAction("/comfyui/addserver", onAddComfyUIServer);
            httpserver.SetHttpAction("/comfyui/getservers", onGetComfyUIServers);
            httpserver.SetHttpAction("/comfyui/deleteserver", onDeleteComfyUIServer);
            httpserver.SetHttpAction("/comfyui/checkserver", onCheckComfyUIServer);



            var IS_LOG = Environment.GetEnvironmentVariable("IS_LOG") != "FALSE";
            //httpserver.Start(7777,7778, PatchUtil.ResRootPatch + "/WebsocketKey/9309951_dai.cafegame.cn.pfx", "rq1ghtek");
            httpserver.Start(7778);
        }
        static int postCount = 0;

        static async Task OnSetBaseTemplate(HttpContext context)
        {
            await order(context, ProcessType.basetemplate);
        }
        static async Task onUserInput(HttpContext context)
        {
            //order
            await order(context, ProcessType.userInput);
        }
        static async Task onRefreshGamebase(HttpContext context)
        {
            await order(context, ProcessType.refreshGamebase);
        }
        static async Task onIGNreviewlink(HttpContext context)
        {
            await order(context, ProcessType.IGNreviewlink);
        }
        static async Task onGameIntroduce(HttpContext context)
        {
            await order(context, ProcessType.GameIntroduce);
        }
        static async Task onChapter(HttpContext context)
        {
            await order(context, ProcessType.Chapter);
        }
        static async Task onCharacterKeys(HttpContext context)
        {
            await order(context, ProcessType.CharacterKeys);
        }
        static async Task onScenceKeys(HttpContext context)
        {
            await order(context, ProcessType.ScenceKeys);
        }
        static async Task onScenceMusic(HttpContext context)
        {
            await order(context, ProcessType.ScenceMusic);
        }
        static async Task onScenceEffect(HttpContext context)
        {
            await order(context, ProcessType.ScenceEffect);
        }
        static async Task onSkybox(HttpContext context)
        {
            await order(context, ProcessType.skybox);
        }
        static async Task onDesignCases(HttpContext context)
        {
            await order(context, ProcessType.designCases);
        }
        static async Task onRefreshExcel(HttpContext context)
        {

            ExcelDataManager.Instance.RefreshDatabaseTables();
        }



        static async Task order(HttpContext context, ProcessType processtype)
        {
            Console.WriteLine("api userlogin");
            postCount++;
            if (context.Request.Method == "OPTIONS")
            {
                context.Response.StatusCode = 200;
                context.Response.Headers.Add("Allow", "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS");
                context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
                await context.Response.WriteAsync("");
            }
            else if (context.Request.Method == "POST")
            {
                string[] contentType = context.Request.ContentType.Split(';');
                if (contentType[0] == "application/json" || contentType[0] == "application/x-www-form-urlencoded")
                {
                    int filesize = (int)context.Request.ContentLength;
                    RequestBackInfo backInfo = null;
                    using (var file = new MemoryStream())
                    {
                        int peekpos = 0;
                        byte[] buf = new byte[4096];
                        while (peekpos < filesize)
                        {
                            try
                            {
                                int read = await context.Request.Body.ReadAsync(buf, 0, buf.Length);
                                peekpos += read;
                                if (read > 0)
                                {
                                    await file.WriteAsync(buf, 0, read);
                                }
                            }
                            catch (Exception err)
                            {
                                context.Response.StatusCode = 400;
                                await context.Response.WriteAsync(err.Message);
                                return;
                            }
                        }
                        string json = System.Text.Encoding.UTF8.GetString(file.ToArray());

                        RequestInfoData RequestInfo = JsonConvert.DeserializeObject<RequestInfoData>(json);
                        backInfo = new RequestBackInfo();

                        switch (processtype)
                        {
                            case ProcessType.basetemplate:
                                backInfo.content = await DeepseekManager.Instance.SetBaseTemplate();
                                backInfo.recode = 200;
                                backInfo.result = "OK";
                                break;
                            case ProcessType.userInput:
                                await DeepseekManager.Instance.SetBaseTemplate();
                                backInfo.content = await DeepseekManager.Instance.SetGameBaseType(RequestInfo.userInputStr);
                                break;
                            case ProcessType.refreshGamebase:
                                backInfo.content = await DeepseekManager.Instance.RefreshGameBaseType(RequestInfo.theme, RequestInfo.gametype, RequestInfo.gameangleOfview, RequestInfo.dimension, RequestInfo.worldview);
                                backInfo.ignJson = await DeepseekManager.Instance.GetIGNreviewlink();
                                backInfo.gameIntroduceJson = await DeepseekManager.Instance.SetGameIntroduce();
                                break;
                            //case ProcessType.IGNreviewlink:
                            //    backInfo.content = await DeepseekManager.Instance.GetIGNreviewlink();
                            //    break;
                            //case ProcessType.GameIntroduce:
                            //    backInfo.content = await DeepseekManager.Instance.SetGameIntroduce();
                            //    break;
                            case ProcessType.Chapter:
                                backInfo.content = await DeepseekManager.Instance.GenerateChapter();
                                break;
                            case ProcessType.CharacterKeys:
                                backInfo.content = await DeepseekManager.Instance.GenerateCharacters();
                                break;
                            case ProcessType.ScenceKeys:
                                backInfo.content = await DeepseekManager.Instance.GenerateScences();
                                break;
                            case ProcessType.ScenceMusic:
                                backInfo.content = await DeepseekManager.Instance.GenerateScenceMusic();
                                break;
                            case ProcessType.ScenceEffect:
                                backInfo.content = await DeepseekManager.Instance.GenerateEffects();
                                break;
                            case ProcessType.skybox:
                                backInfo.content = await DeepseekManager.Instance.GenerateSkyboxTemplate();
                                break;
                            case ProcessType.designCases:
                                await DeepseekManager.Instance.GenerateDesignCases();
                                backInfo.content = "生成策划案完成。";
                                break;

                        }
                        if (backInfo.content != "")
                        {
                            backInfo.recode = 200;
                            backInfo.result = "OK";
                        }
                        else
                        {
                            backInfo.recode = 400;
                            backInfo.result = "未生成游戏内容";
                        }
                    }

                    var reqInfo = JsonConvert.SerializeObject(backInfo);
                    context.Response.StatusCode = backInfo.recode;
                    context.Response.ContentType = "application/json;charset=UTF-8";
                    context.Response.Headers.Add("Allow", "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS");
                    context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                    context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

                    await context.Response.WriteAsync(reqInfo);
                }
                else
                {
                    await context.Response.WriteAsync("not support contentType=" + context.Request.ContentType);
                }
            }
            else
            {
                await context.Response.WriteAsync("error method");
            }
        }


        static async Task on404(HttpContext context)
        {
            await context.Response.WriteAsync("only websocket connect.");
            return;
        }

        #region ComfyUI服务器管理接口

        /// <summary>
        /// 添加ComfyUI服务器
        /// </summary>
        static async Task onAddComfyUIServer(HttpContext context)
        {
            await ProcessComfyUIRequest(context, async (requestData) =>
            {
                var result = await ComfyUIServerManager.AddServerAsync(
                    requestData.serverName ?? "",
                    requestData.ip ?? "",
                    requestData.port,
                    requestData.description ?? ""
                );
                return result;
            });
        }

        /// <summary>
        /// 获取所有ComfyUI服务器
        /// </summary>
        static async Task onGetComfyUIServers(HttpContext context)
        {
            await ProcessComfyUIRequest(context, async (requestData) =>
            {
                return await ComfyUIServerManager.GetAllServersAsync();
            });
        }

        /// <summary>
        /// 删除ComfyUI服务器
        /// </summary>
        static async Task onDeleteComfyUIServer(HttpContext context)
        {
            await ProcessComfyUIRequest(context, async (requestData) =>
            {
                return await ComfyUIServerManager.DeleteServerAsync(requestData.serverId);
            });
        }

        /// <summary>
        /// 检查ComfyUI服务器连通性
        /// </summary>
        static async Task onCheckComfyUIServer(HttpContext context)
        {
            await ProcessComfyUIRequest(context, async (requestData) =>
            {
                var server = new ComfyUIServerConfig
                {
                    ip = requestData.ip ?? "",
                    port = requestData.port
                };
                var connectivityResult = await ComfyUIServerManager.CheckServerConnectivity(server);
                return new ComfyUIServerResult
                {
                    Success = true,
                    Message = "连通性检查完成",
                    ConnectivityInfo = connectivityResult
                };
            });
        }

        /// <summary>
        /// 处理ComfyUI请求的通用方法
        /// </summary>
        static async Task ProcessComfyUIRequest(HttpContext context, Func<ComfyUIRequestData, Task<ComfyUIServerResult>> processor)
        {
            try
            {
                // 设置CORS头
                context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
                context.Response.Headers.Add("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS");

                if (context.Request.Method == "OPTIONS")
                {
                    context.Response.StatusCode = 200;
                    await context.Response.WriteAsync("");
                    return;
                }

                if (context.Request.Method != "POST")
                {
                    context.Response.StatusCode = 405;
                    await context.Response.WriteAsync("只支持POST方法");
                    return;
                }

                // 读取请求体
                string requestBody;
                using (var reader = new StreamReader(context.Request.Body))
                {
                    requestBody = await reader.ReadToEndAsync();
                }

                ComfyUIRequestData requestData = null;
                if (!string.IsNullOrEmpty(requestBody))
                {
                    try
                    {
                        requestData = JsonConvert.DeserializeObject<ComfyUIRequestData>(requestBody);
                    }
                    catch (JsonException)
                    {
                        context.Response.StatusCode = 400;
                        await context.Response.WriteAsync("JSON格式错误");
                        return;
                    }
                }

                requestData = requestData ?? new ComfyUIRequestData();

                // 处理请求
                var result = await processor(requestData);

                // 返回结果
                context.Response.ContentType = "application/json;charset=UTF-8";
                context.Response.StatusCode = result.Success ? 200 : 400;

                var responseJson = JsonConvert.SerializeObject(result, Formatting.Indented);
                await context.Response.WriteAsync(responseJson);
            }
            catch (Exception ex)
            {
                context.Response.StatusCode = 500;
                context.Response.ContentType = "application/json;charset=UTF-8";
                var errorResult = new ComfyUIServerResult
                {
                    Success = false,
                    Message = $"服务器内部错误: {ex.Message}",
                    ErrorCode = "INTERNAL_ERROR"
                };
                var errorJson = JsonConvert.SerializeObject(errorResult);
                await context.Response.WriteAsync(errorJson);
            }
        }

        #endregion
    }

    public class RequestBackInfo
    {
        public string result;
        public int recode;
        public string content;
        public string ignJson;
        public string gameIntroduceJson;
    }

    public class RequestInfoData
    {
        public string userInputStr;
        public string theme;
        public string gametype;
        public string gameangleOfview;
        public string dimension;
        public string worldview;
    }
    public enum ProcessType
    {
        basetemplate,
        userInput,
        refreshGamebase,
        IGNreviewlink,
        GameIntroduce,
        Chapter,
        CharacterKeys,
        ScenceKeys,
        ScenceMusic,
        ScenceEffect,
        skybox,
        designCases
    }

    /// <summary>
    /// ComfyUI请求数据
    /// </summary>
    public class ComfyUIRequestData
    {
        public string serverName { get; set; } = "";
        public string ip { get; set; } = "";
        public int port { get; set; } = 8188;
        public string description { get; set; } = "";
        public uint serverId { get; set; } = 0;
    }
}
