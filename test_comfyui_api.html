<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComfyUI服务器管理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .server-list {
            display: grid;
            gap: 10px;
        }
        .server-item {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .server-status {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-online { background-color: #d4edda; color: #155724; }
        .status-offline { background-color: #f8d7da; color: #721c24; }
        .status-maintenance { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>ComfyUI服务器管理测试页面</h1>
    
    <!-- 添加服务器 -->
    <div class="container">
        <h2>添加ComfyUI服务器</h2>
        <div class="form-group">
            <label for="serverName">服务器名称:</label>
            <input type="text" id="serverName" placeholder="例如: 本地ComfyUI服务器">
        </div>
        <div class="form-group">
            <label for="serverIp">IP地址:</label>
            <input type="text" id="serverIp" placeholder="例如: 127.0.0.1" value="127.0.0.1">
        </div>
        <div class="form-group">
            <label for="serverPort">端口:</label>
            <input type="number" id="serverPort" placeholder="例如: 8188" value="8188">
        </div>
        <div class="form-group">
            <label for="serverDescription">描述:</label>
            <textarea id="serverDescription" placeholder="服务器描述信息"></textarea>
        </div>
        <button onclick="addServer()">添加服务器</button>
        <div id="addResult" class="result" style="display: none;"></div>
    </div>

    <!-- 服务器列表 -->
    <div class="container">
        <h2>服务器列表</h2>
        <button onclick="getServers()">刷新列表</button>
        <div id="serverList" class="server-list"></div>
        <div id="listResult" class="result" style="display: none;"></div>
    </div>

    <!-- 连通性检查 -->
    <div class="container">
        <h2>连通性检查</h2>
        <div class="form-group">
            <label for="checkIp">IP地址:</label>
            <input type="text" id="checkIp" placeholder="例如: 127.0.0.1" value="127.0.0.1">
        </div>
        <div class="form-group">
            <label for="checkPort">端口:</label>
            <input type="number" id="checkPort" placeholder="例如: 8188" value="8188">
        </div>
        <button onclick="checkConnectivity()">检查连通性</button>
        <div id="checkResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:7778';

        async function addServer() {
            const serverName = document.getElementById('serverName').value;
            const ip = document.getElementById('serverIp').value;
            const port = parseInt(document.getElementById('serverPort').value);
            const description = document.getElementById('serverDescription').value;

            if (!serverName || !ip || !port) {
                showResult('addResult', '请填写所有必填字段', false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/comfyui/addserver`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        serverName,
                        ip,
                        port,
                        description
                    })
                });

                const result = await response.json();
                showResult('addResult', JSON.stringify(result, null, 2), result.Success);

                if (result.Success) {
                    // 清空表单
                    document.getElementById('serverName').value = '';
                    document.getElementById('serverDescription').value = '';
                    // 刷新服务器列表
                    getServers();
                }
            } catch (error) {
                showResult('addResult', `请求失败: ${error.message}`, false);
            }
        }

        async function getServers() {
            try {
                const response = await fetch(`${API_BASE}/comfyui/getservers`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                });

                const result = await response.json();
                
                if (result.Success && result.Data) {
                    displayServerList(result.Data);
                    showResult('listResult', `成功获取 ${result.Data.length} 个服务器`, true);
                } else {
                    showResult('listResult', JSON.stringify(result, null, 2), false);
                }
            } catch (error) {
                showResult('listResult', `请求失败: ${error.message}`, false);
            }
        }

        function displayServerList(servers) {
            const listContainer = document.getElementById('serverList');
            listContainer.innerHTML = '';

            servers.forEach(server => {
                const serverDiv = document.createElement('div');
                serverDiv.className = 'server-item';
                
                const statusClass = server.status === 1 ? 'status-online' : 
                                  server.status === 2 ? 'status-maintenance' : 'status-offline';
                const statusText = server.status === 1 ? '在线' : 
                                 server.status === 2 ? '维护' : '离线';

                serverDiv.innerHTML = `
                    <div style="display: flex; justify-content: between; align-items: center;">
                        <div>
                            <strong>${server.serverName}</strong>
                            <span class="server-status ${statusClass}">${statusText}</span>
                        </div>
                        <button onclick="deleteServer(${server.dbID})" style="width: auto; padding: 5px 10px; margin-left: auto;">删除</button>
                    </div>
                    <div>地址: ${server.ip}:${server.port}</div>
                    <div>描述: ${server.description || '无'}</div>
                    <div>创建时间: ${new Date(server.createTime).toLocaleString()}</div>
                `;
                
                listContainer.appendChild(serverDiv);
            });
        }

        async function deleteServer(serverId) {
            if (!confirm('确定要删除这个服务器吗？')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/comfyui/deleteserver`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ serverId })
                });

                const result = await response.json();
                showResult('listResult', result.Message, result.Success);

                if (result.Success) {
                    getServers(); // 刷新列表
                }
            } catch (error) {
                showResult('listResult', `删除失败: ${error.message}`, false);
            }
        }

        async function checkConnectivity() {
            const ip = document.getElementById('checkIp').value;
            const port = parseInt(document.getElementById('checkPort').value);

            if (!ip || !port) {
                showResult('checkResult', '请填写IP地址和端口', false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/comfyui/checkserver`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ ip, port })
                });

                const result = await response.json();
                showResult('checkResult', JSON.stringify(result, null, 2), result.Success);
            } catch (error) {
                showResult('checkResult', `请求失败: ${error.message}`, false);
            }
        }

        function showResult(elementId, message, isSuccess) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.style.display = 'block';
        }

        // 页面加载时获取服务器列表
        window.onload = function() {
            getServers();
        };
    </script>
</body>
</html>
