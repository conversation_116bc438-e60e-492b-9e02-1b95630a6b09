﻿using Newtonsoft.Json;
using static AISDKWebSocket.HttpManager;

namespace AISdk.Manage
{
	internal class LingYiAISDK : AISDKProvider
	{
		private string url = "https://api.lingyiwanwu.com/v1/chat/completions";
		private string key = "Bearer e8525a30f15343728754f939ca69c979";
		public string Model;
		public LingYiAISDK(string userMode)
		{
			this.Model = userMode;
		}
		public void SendTextToAIModel(List<string> text, Action<string, bool> callback)
		{
			List<Message> messages = new List<Message>();
			for (int i = 0; i < text.Count; i++)
			{
				if (i % 2 != 0)
				{
					messages.Add(new Message { role = "assistant", content = text[i] });
				}
				else
				{
					messages.Add(new Message { role = "user", content = text[i] });
				}
			}
			if (string.IsNullOrEmpty(Model))
			{
				Model = "yi-large-turbo";
			}

			var data = new KuaiShuData(Model, "user", messages, true);
			//string jsonData = JsonConvert.SerializeObject(data);

			Instance.HttpsWeb(url, key, data, (backText, bol) =>
			{
				//Console.WriteLine(backText);
				if (!bol)
				{
					backText = this.Model + ": " + backText;
				}
				callback(backText, bol);
			});
		}
        //根据输入的图像内容、视频内容和自然语言指令完成任务
        public void SendImageOrVideoToAiModle(string videoBase64Str, string requestDescrib, Action<string, bool> callback)
        {
            if (string.IsNullOrEmpty(Model))
            {
                Model = "yi-large-turbo";
            }
            string backText = Model + ": " + "暂不支持分析视频，请切换至智谱大模型";
            callback(backText, false);
        }
    }
}
