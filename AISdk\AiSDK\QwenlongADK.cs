﻿using Newtonsoft.Json;
using System.Reflection;
using static AISDKWebSocket.HttpManager;

namespace AISdk.Manage
{
	internal class QwenlongADK : AISDKProvider
	{
		private string url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
		private string key = "Bearer sk-35b4f54ced9a404fa05fa939da455e4a";
		public string Model;
		public QwenlongADK(string userMode)
		{
			this.Model = userMode;
		}
		public void SendTextToAIModel(List<string> text, Action<string, bool> callback)
		{
			List<Message> messages = new List<Message>();
			for (int i = 0; i < text.Count; i++)
			{
				if (i % 2 != 0)
				{
					messages.Add(new Message { role = "assistant", content = text[i] });
				}
				else
				{
					messages.Add(new Message { role = "user", content = text[i] });
				}
			}
			if (string.IsNullOrEmpty(Model))
			{
				Model = "qwen-max";
			}

			var data = new KuaiShuData(Model, "user", messages, true);

			//string jsonData = JsonConvert.SerializeObject(data);

			Instance.HttpsWeb(url, key, data, (backText, bol) =>
			{
				// UnityEngine.Debug.Log(backText);
				if (!bol)
				{
					backText = this.Model + ": " + backText;
				}
				callback(backText, bol);
			});
		}

        //根据输入的图像内容、视频内容和自然语言指令完成任务
        public void SendImageOrVideoToAiModle(string videoBase64Str, string requestDescrib, Action<string, bool> callback)
        {
            if (string.IsNullOrEmpty(Model))
            {
                Model = "qwen-max";
            }
            string backText = Model + ": " + "暂不支持分析视频，请切换至智谱大模型";
            callback(backText, false);
        }
    }
}
