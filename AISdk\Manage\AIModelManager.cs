﻿
namespace AISdk.Manage
{
	public enum AIModelPlatform
	{
		智谱清言,// 使用上限
		DeepSeek, //使用上限
		通义千问,
		百川,
		零一万物,
		星火,
		MM,
		豆包,
		OneChat
	}
	public class AIModelManager
	{
		private static AIModelManager _instance;

		public static AIModelManager Instance
		{
			get
			{
				if (_instance == null)
				{
					_instance = new AIModelManager();
				}
				return _instance;
			}
		}

		public AISDKProvider aiSdk;

		//private AIModelPlatform useAIModel= AIModelPlatform.XunFei;
		public void init(int useAIModel, string Model = "")
		{
			switch (useAIModel)
			{
				case (int)AIModelPlatform.智谱清言:
					aiSdk = new XunFeiAISDK(Model);
					break;
				case (int)AIModelPlatform.DeepSeek:
					aiSdk = new KuaiShuAISDK(Model);
					break;
				case (int)AIModelPlatform.通义千问:
					aiSdk = new QwenlongADK(Model);
					break;
				case (int)AIModelPlatform.百川:
					aiSdk = new BaiCuanAISDK(Model);
					break;
				case (int)AIModelPlatform.零一万物:
					aiSdk = new LingYiAISDK(Model);
					break;
				case (int)AIModelPlatform.星火:
					aiSdk = new XinghuoAISDK(Model);
					break;
				case (int)AIModelPlatform.MM:
					aiSdk = new MM_AISDK(Model);
					break;
				case (int)AIModelPlatform.豆包:
					aiSdk = new DoBaoAISDK(Model);
					break;
				case (int)AIModelPlatform.OneChat:
					aiSdk = new OneChat(Model);
					break;
				case 9:
					aiSdk = new Juyuwang();
					break;
			}
		}
	}

}
