# ComfyUI服务器管理功能

本功能为您的项目添加了完整的ComfyUI服务器管理能力，包括添加、查询、删除服务器配置，以及重复检查和连通性检测。

## 🚀 主要功能

### ✅ 重复检查机制
- **IP+端口组合检查**: 自动检测相同IP和端口的服务器，避免重复添加
- **服务器名称检查**: 防止使用重复的服务器名称
- **详细错误信息**: 当检测到重复时，会显示现有服务器的详细信息

### ✅ 连通性检测
- **自动检测**: 添加服务器时自动检查连通性
- **响应时间测量**: 记录服务器响应时间
- **状态更新**: 根据连通性自动更新服务器状态

### ✅ 输入验证
- **IP地址格式验证**: 确保IP地址格式正确
- **端口范围检查**: 验证端口在有效范围内(1-65535)
- **必填字段检查**: 确保所有必要信息都已填写

## 📁 文件结构

```
SaveDataService/
├── ORMModel.cs                           # 添加了ComfyUIServerConfig类
├── Manage/
│   ├── ComfyUIServerManager.cs          # 核心管理逻辑
│   ├── PostManager.cs                   # HTTP API接口
│   └── ExcelDataManager.cs              # 数据库管理
└── Demo/
    └── ComfyUIServerTest.cs             # 测试代码

根目录/
├── ComfyUI_API_Documentation.md         # API文档
├── test_comfyui_api.html               # 测试页面
└── ComfyUI_Server_Management_README.md  # 本文件
```

## 🛠️ 安装和配置

### 1. 数据库配置
系统会自动创建 `comfyuiserverconfigs` 表，包含以下字段：
- `dbID`: 主键ID
- `serverName`: 服务器名称
- `ip`: IP地址
- `port`: 端口号
- `description`: 描述信息
- `isEnabled`: 是否启用
- `createTime`: 创建时间
- `updateTime`: 更新时间
- `status`: 服务器状态(0:离线, 1:在线, 2:维护)

### 2. API端点
服务器启动后，以下API端点将可用：
- `POST /comfyui/addserver` - 添加服务器
- `POST /comfyui/getservers` - 获取服务器列表
- `POST /comfyui/deleteserver` - 删除服务器
- `POST /comfyui/checkserver` - 检查服务器连通性

## 📖 使用示例

### 添加服务器（避免重复）

```csharp
// 第一次添加 - 成功
var result1 = await ComfyUIServerManager.AddServerAsync(
    "本地服务器", "127.0.0.1", 8188, "开发环境"
);
// result1.Success = true

// 尝试添加相同IP和端口 - 失败
var result2 = await ComfyUIServerManager.AddServerAsync(
    "重复服务器", "127.0.0.1", 8188, "这会被拒绝"
);
// result2.Success = false
// result2.ErrorCode = "DUPLICATE_SERVER"
// result2.Message = "服务器 127.0.0.1:8188 已存在！现有服务器名称: 本地服务器"
```

### 检查连通性

```csharp
var server = new ComfyUIServerConfig 
{ 
    ip = "127.0.0.1", 
    port = 8188 
};

var connectivity = await ComfyUIServerManager.CheckServerConnectivity(server);
Console.WriteLine($"服务器状态: {(connectivity.IsOnline ? "在线" : "离线")}");
Console.WriteLine($"响应时间: {connectivity.ResponseTime}ms");
```

### 获取服务器列表

```csharp
var result = await ComfyUIServerManager.GetAllServersAsync();
if (result.Success && result.Data is List<ComfyUIServerConfig> servers)
{
    foreach (var server in servers)
    {
        Console.WriteLine($"{server.serverName}: {server.GetFullAddress()}");
    }
}
```

## 🧪 测试

### 1. 单元测试
运行内置的测试类：

```csharp
await ComfyUIServerTest.RunAllTests();
await ComfyUIServerTest.TestInputValidation();
```

### 2. Web界面测试
1. 启动服务器
2. 在浏览器中打开 `test_comfyui_api.html`
3. 测试各种功能

### 3. API测试
使用cURL或Postman测试API：

```bash
# 添加服务器
curl -X POST http://localhost:7778/comfyui/addserver \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "测试服务器",
    "ip": "127.0.0.1",
    "port": 8188,
    "description": "测试用"
  }'
```

## ⚠️ 重要说明

### 重复检查逻辑
1. **IP+端口组合**: 系统检查IP地址和端口的组合，不允许重复
2. **大小写不敏感**: IP地址比较时忽略大小写
3. **空格处理**: 自动去除IP地址和服务器名称的前后空格
4. **服务器名称**: 不允许重复的服务器名称

### 错误处理
- 所有操作都有详细的错误信息和错误代码
- 网络错误、超时等情况都有相应的处理
- 输入验证确保数据的完整性和正确性

### 性能考虑
- 连通性检查有10秒超时限制
- 数据库操作使用异步方法
- 支持并发请求

## 🔧 自定义配置

### 修改连通性检查超时
在 `ComfyUIServerManager.cs` 中修改：

```csharp
private static readonly HttpClient httpClient = new HttpClient() 
{ 
    Timeout = TimeSpan.FromSeconds(30) // 修改为30秒
};
```

### 修改检查端点
默认检查 `/system_stats` 端点，可以修改为其他端点：

```csharp
var response = await httpClient.GetAsync($"{server.GetFullAddress()}/your_endpoint");
```

### 添加自定义验证
在 `ValidateServerInput` 方法中添加自定义验证逻辑。

## 📞 支持

如果您在使用过程中遇到问题：

1. 查看API文档了解详细的接口说明
2. 运行测试代码验证功能
3. 检查服务器日志获取错误信息
4. 确保ComfyUI服务器正在运行并可访问

## 🎯 使用场景

这个功能特别适合以下场景：

1. **多环境管理**: 管理开发、测试、生产环境的ComfyUI服务器
2. **负载均衡**: 配置多个ComfyUI服务器实现负载分担
3. **故障转移**: 当主服务器不可用时切换到备用服务器
4. **集中管理**: 在一个地方管理所有ComfyUI服务器配置
5. **监控检查**: 定期检查服务器状态和连通性
