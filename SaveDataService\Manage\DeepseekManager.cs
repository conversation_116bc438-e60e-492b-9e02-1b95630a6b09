﻿using ExcelToData;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using NetService;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HPSF;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace SaveDataService.Manage
{
    public class DeepseekManager
    {
        // 私有静态实例变量
        private static DeepseekManager _instance;
        // 线程安全锁对象
        private static readonly object _lock = new object();
        // 私有构造函数，防止外部实例化
        private DeepseekManager()
        {
            // 初始化代码
        }
        // 公共静态属性，用于访问单例实例
        public static DeepseekManager Instance
        {
            get
            {
                // 双重检查锁定模式确保线程安全
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new DeepseekManager();
                        }
                    }
                }
                return _instance;
            }
        }
        //List<AI_Game_Output> AIGameOutPutlist = new List<AI_Game_Output>();

        string deepseekApiKey = "***********************************";
        string deepseekModel = "deepseek-chat";
        string deepseekUri = "https://api.deepseek.com/v1";
        string baseTemplatePath = "baseTemplate";
        public IChatCompletionService ChatService;
        ChatHistory chatHistory = new ChatHistory(); // 全局 ChatHistory
        string filePath = "";

        /// <summary>
        /// 生成一系列json的模板关键词
        /// </summary>
        string gameBaseJsonKeys = "";
        string ignlinkJsonKeys = "";
        string gameintroduceKeys = "";
        string chapterJsonKeys = "";
        string characterJsonKeys = "";
        string scenceJsonKeys = "";
        string scencekeysJsonKeys = "";
        string scencemusicJsonKeys = "";
        string scenceeffectsJsonKeys = "";
        string scenceskyboxJsonKeys = "";
        public async Task init()
        {
            GetAIGameOutput();
            Console.WriteLine("DeepSeek AI 游戏策划内容生成器 (输入 'exit' 退出)");
            var kernel = Kernel.CreateBuilder()
          .AddOpenAIChatCompletion(
               modelId: deepseekModel, // 模型名称
               apiKey: deepseekApiKey, // 替换为你的 API Key
               endpoint: new Uri(deepseekUri) // DeepSeek API 地址
          )
          .Build();

            // 获取聊天服务
            ChatService = kernel.GetRequiredService<IChatCompletionService>();

            while (true)
            {
                Console.Write("> ");
                var userInput = Console.ReadLine();
                if (string.IsNullOrWhiteSpace(userInput))
                    continue;
                if (userInput.Trim().Equals("/exit", StringComparison.OrdinalIgnoreCase))
                    break;

                if (userInput.StartsWith("/basetemplate"))
                {
                    await SetBaseTemplate();
                }
                else if (userInput.StartsWith("/playerdemand "))
                {
                    var prompt = userInput.Substring(14); // 提取用户的需求描述
                    await SetGameBaseType(prompt);
                }
                else if (userInput.StartsWith("/refreshGamebase "))
                {
                    var prompt = userInput.Substring(17); // 提取用户的需求描述
                    string[] parts = prompt.Split("|");
                    string theme = parts[0];
                    string gametype = parts[1];
                    string gameangleOfview = parts[2];
                    string dimension = parts[3];
                    string worldview = parts[4];
                    await RefreshGameBaseType(theme, gametype, gameangleOfview, dimension, worldview);

                    //在这里生成策划案子
                }
                else if (userInput.StartsWith("/IGNreviewlink "))
                {
                    await GetIGNreviewlink();
                }
                else if (userInput.StartsWith("/GameIntroduce "))
                {
                    await SetGameIntroduce();
                }
                else if (userInput.StartsWith("/Chapter "))
                {
                    await GenerateChapter();
                }
                else if (userInput.StartsWith("/CharacterKeys "))
                {
                    await GenerateCharacters();
                }
                else if (userInput.StartsWith("/ScenceKeys "))
                {
                    await GenerateScences();
                }
                else if (userInput.StartsWith("/ScenceMusic "))
                {
                    await GenerateScenceMusic();
                }
                else if (userInput.StartsWith("/ScenceEffect "))
                {
                    await GenerateEffects();
                }
                else if (userInput.StartsWith("/skybox "))
                {
                    await GenerateSkyboxTemplate();
                }
                else if (userInput.StartsWith("/test "))
                {
                }
                else
                {
                    Console.WriteLine("未知命令");
                }
            }
        }
        string charactertemplateKeys1 = "";
        string charactertemplateKeys2 = "";
        string scencetemplateKeys2 = "";
        string skyboxtemplateKeys1 = "";
        string skyboxtemplateKeys2 = "";
        /// <summary>
        /// 获取策划配置表数据
        /// </summary>
        public void GetAIGameOutput()
        {
            setPromptKeys();
        }
        private void setPromptKeys()
        {
            //string templateKeys = ArtAIChatOutPutlist["10001"];
            List<ArtAIChat_Output> list = ArtAIChat_Output.getAllDatasList();
            for (int i = 0; i < list.Count; i++)
            {
                ArtAIChat_Output data = list[i];
                string templateKeys = data.generateTemplate;
                if (templateKeys.IndexOf("{10005_Output}") != -1)
                {
                    string[] parts = templateKeys.Split(new[] { "{10005_Output}" }, StringSplitOptions.None)
                                         .Select(s => s.Trim(',', ' '))
                                         .ToArray();

                    charactertemplateKeys1 = parts[0];
                    charactertemplateKeys2 = parts[1];
                }
                else if (templateKeys.IndexOf("{10006_Output}") != -1)
                {
                    string[] parts = templateKeys.Split(new[] { "{10006_Output}" }, StringSplitOptions.None)
                                         .Select(s => s.Trim(',', ' '))
                                         .ToArray();
                    scencetemplateKeys2 = parts[0];
                }
                else if (templateKeys.IndexOf("{10010_Output}") != -1)
                {
                    string[] parts = templateKeys.Split(new[] { "{10010_Output}" }, StringSplitOptions.None)
                                        .Select(s => s.Trim(',', ' '))
                                        .ToArray();
                    skyboxtemplateKeys1 = parts[0];
                    skyboxtemplateKeys2 = parts[1];
                }
            }
        }
        /// <summary>
        ///  设置前置需求
        /// </summary>
        /// <param name="chatService"></param>
        /// <param name="prompt"></param>
        /// <returns></returns>
        public async Task<string> SetBaseTemplate()
        {
            string basetemplateStr = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10001");
            if (item != null)
            {
                gameBaseJsonKeys = item.jsonModle;
                Console.WriteLine(item.systemPrompt);
                try
                {
                    Console.WriteLine($"正在生成游戏前置需求：{item.systemPrompt}...");
                    chatHistory.Clear(); // 清空历史
                    chatHistory.AddSystemMessage(item.systemPrompt); // 重新添加系统提示
                    if (!Directory.Exists(baseTemplatePath))
                        Directory.CreateDirectory(baseTemplatePath);

                    string outputPath = Path.Combine(baseTemplatePath, "baseTemplate.txt");
                    await File.WriteAllTextAsync(outputPath, item.systemPrompt);
                    basetemplateStr = item.systemPrompt;
                    Console.WriteLine("------ 前置需求已重置 ------");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"生成前置需求失败：{ex.Message}");
                }
            }
            else
            {
                Console.WriteLine($"AI_Game_Output 表中未找到10001 的数据！");
            }

            return basetemplateStr;
        }
        /// <summary>
        /// 设定好前置需求后，根据玩家提出的要求，设置游戏类型 视角 2d/3d  世界观 
        /// </summary>
        /// <param name="chatService"></param>
        /// <param name="prompt"></param>
        /// <returns></returns>
        public async Task<string> SetGameBaseType(string prompt)
        {
            string outStr = "";
            try
            {
                AI_Game_Output item = AI_Game_Output.getDataById("10001");
                if (item != null)
                {
                    Console.WriteLine($"正在根据玩家需求生成游戏基本信息：{prompt}...");
                    outStr = await ChatWithAI(prompt, "GameBase", true, item.ifMarkdown, item.ifJson);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成游戏基本信息失败：{ex.Message}");
            }
            return outStr;
        }
        /// <summary>
        /// 玩家手边手动修改了游戏基础信息，重新生成刷新gamebase
        /// </summary>
        /// <param name="chatService">聊天服务接口</param>
        /// <param name="theme">游戏题材</param>
        /// <param name="gametype">游戏类型(RPG/FPS/策略等)</param>
        /// <param name="gameangleOfview">游戏视角(第一人称/第三人称等)</param>
        /// <param name="dimension">游戏维度(2D/3D等)</param>
        /// <param name="worldview">游戏世界观设定</param>
        /// <returns>异步任务</returns>
        public async Task<string> RefreshGameBaseType(string theme, string gametype, string gameangleOfview, string dimension, string worldview)
        {
            string outstr = "";
            string prompt = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10009");
            if (item != null)
            {
                gameBaseJsonKeys = item.jsonModle;
                prompt = item.generateTemplate;
                prompt = Regex.Replace(prompt, @"\{PlayerInt_题材\}", theme);
                prompt = Regex.Replace(prompt, @"\{PlayerInt_游戏类型\}", gametype);
                prompt = Regex.Replace(prompt, @"\{PlayerInt_游戏视角\}", gameangleOfview);
                prompt = Regex.Replace(prompt, @"\{PlayerInt_dimension\}", dimension);
                prompt = Regex.Replace(prompt, @"\{PlayerInt_世界观\}", worldview);
                try
                {
                    if (!string.IsNullOrEmpty(prompt))
                    {

                        Console.WriteLine($"正在重新修改模板内容：...");
                        chatHistory.Clear(); // 清空历史
                        chatHistory.AddUserMessage(prompt); // 添加用户输入到历史

                        var generatedContent = new StringBuilder();
                        await foreach (var chunk in ChatService.GetStreamingChatMessageContentsAsync(chatHistory))
                        {
                            Console.Write(chunk.Content);
                            generatedContent.Append(chunk.Content);
                        }

                        // 从生成内容中提取游戏名（假设第一行是游戏名）
                        string generatedText = generatedContent.ToString();
                        string gameNameLine = generatedText.Split('\n').FirstOrDefault() ?? "游戏名：《未知》";
                        // 构建基础信息
                        StringBuilder newresult = new StringBuilder();
                        newresult.AppendLine(gameNameLine); // 使用生成内容中的游戏名
                        newresult.AppendLine($"题材：{theme}");
                        newresult.AppendLine($"游戏类型：{gametype}");
                        newresult.AppendLine($"游戏视角：{gameangleOfview}");
                        newresult.AppendLine($"3D或2D：{dimension}");
                        newresult.AppendLine($"世界观：{worldview}");
                        // 添加生成内容中除第一行（游戏名）外的其余内容
                        string remainingContent = string.Join("\n", generatedText.Split('\n').Skip(1));
                        newresult.AppendLine(remainingContent);
                        //outstr= newresult.ToString();
                        Console.WriteLine($"\n刷新后的gamebase: \n{newresult.ToString()}");
                        // AI 的回复会自动添加到 chatHistory 中，后续对话可以基于此继续
                        if (item.ifMarkdown == true)
                        {
                            await GenerateMDfile(newresult.ToString(), "GameBase");
                        }
                        outstr = await GenerateGameBaseJson(newresult.ToString(), "GameBase");
                    }
                    else
                    {
                        Console.WriteLine("\n------ 重新修改Gamebase内容 失败 ------");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"重新修改Gamebase内容失败：{ex.Message}");
                }
            }
            return outstr;
        }
        /// <summary>
        /// 根据给到的游戏题材，类型，视角，3d/2d 获取相关要素的游戏代表作的IGN 评测链接
        /// </summary>
        /// <param name="chatService"></param>
        /// <returns></returns>
        public async Task<string> GetIGNreviewlink()
        {
            string outstr = "";
            string prompt = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10002");
            if (item != null)
            {
                ignlinkJsonKeys = item.jsonModle;
                //bool generateMDbol = item.ifMarkdown;
                Console.WriteLine($"{item.generateTemplate} ");
                string IGNreviewlink = Regex.Replace(item.generateTemplate, @"{.*?_Output*?}\n?", "");
                string sysytemStr = item.systemPrompt;
                try
                {
                    GameBase game = GetGameBaseData();
                    string theme = game.theme;
                    string gameType = game.gametype;
                    string viewPerspective = game.perspective;
                    string dimension = game.dimension;
                    // 构建最终字符串
                    StringBuilder result = new StringBuilder();
                    result.AppendLine($"游戏题材：{theme}");
                    result.AppendLine($"游戏类型：{gameType}");
                    result.AppendLine($"游戏视角：{viewPerspective}");
                    result.AppendLine($"3D或2D：{dimension}");
                    result.Append(IGNreviewlink);
                    Console.WriteLine(result.ToString());
                    prompt = result.ToString();

                    try
                    {
                        if (!string.IsNullOrEmpty(prompt))
                        {
                            Console.WriteLine($"正在根据游戏要素生成游戏代表作的IGN 评测链接：...");
                            chatHistory.Clear(); // 清空历史
                            chatHistory.AddSystemMessage(sysytemStr); // 重新添加系统提示
                            outstr = await ChatWithAI(prompt, "IGNreviewlink", true, item.ifMarkdown, item.ifJson);
                        }
                        else
                        {
                            Console.WriteLine("\n------ 获取生成IGN 评测连接 prompt 失败 ------");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"生成IGN 评测连接失败：{ex.Message}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("解析json 失败！");
                }
            }
            return outstr;
        }
        /// <summary>
        /// 生成游戏简介
        /// </summary>
        /// <param name="chatService"></param>
        /// <returns></returns>
        public async Task<string> SetGameIntroduce()
        {
            string outstr = "";
            string prompt = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10003");
            if (item != null)
            {
                gameintroduceKeys = item.jsonModle;
                prompt = item.generateTemplate;
                string outputPath = Path.Combine(baseTemplatePath, "GameBase.json");
                string jsonString = File.ReadAllText(outputPath);
                prompt = Regex.Replace(prompt, @"\{10001_Output\}", jsonString);
                Console.WriteLine(prompt.ToString());
                try
                {
                    if (!string.IsNullOrEmpty(prompt))
                    {

                        Console.WriteLine($"正在生成游戏简介：...");
                        chatHistory.Clear(); // 清空历史
                        chatHistory.AddSystemMessage(item.systemPrompt); // 重新添加系统提示
                        outstr = await ChatWithAI(prompt, "GameIntroduce", true, item.ifMarkdown, item.ifJson);
                    }
                    else
                    {
                        Console.WriteLine("\n------生成游戏简介 失败 ------");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"生成IGN 评测连接失败：{ex.Message}");
                }
            }
            return outstr;
        }
        /// <summary>
        /// 根据世界观 游戏剧情  生成游戏章节剧本
        /// </summary>
        /// <param name="chatService"></param>
        /// <returns></returns>
        public async Task<string> GenerateChapter()
        {
            string outstr = "";
            string prompt = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10004");
            if (item != null)
            {
                chapterJsonKeys = item.jsonModle;
                prompt = item.generateTemplate;
                GameBase game = GetGameBaseData();
                prompt = Regex.Replace(prompt, @"\{10001_Output_世界观\}", game.worldview);
                prompt = Regex.Replace(prompt, @"\{10001_Output_游戏剧情\}", game.story);
                Console.WriteLine(prompt.ToString());
                try
                {
                    if (!string.IsNullOrEmpty(prompt))
                    {

                        Console.WriteLine($"正在生成游戏章节剧本：...");
                        chatHistory.Clear(); // 清空历史
                        chatHistory.AddSystemMessage(item.systemPrompt); // 重新添加系统提示
                        outstr = await ChatWithAI(prompt, "Chapter", false, item.ifMarkdown, item.ifJson);
                    }
                    else
                    {
                        Console.WriteLine("\n------生成游戏章节剧本 失败 ------");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"生成游戏章节剧本失败：{ex.Message}");
                }
            }
            return outstr;
        }

        /// <summary>
        /// 根据游戏简介生成角色关键词
        /// </summary>
        /// <param name="chatService"></param>
        /// <returns></returns>
        public async Task<string> GenerateCharacters()
        {
            string outstr = "";
            string prompt = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10005");
            if (item != null)
            {
                characterJsonKeys = item.jsonModle;
                string characterKey = Regex.Replace(item.generateTemplate, @"{.*?_Output*?}\n?", "");
                string sysytemStr = item.systemPrompt;

                //string outputPath = Path.Combine(baseTemplatePath, "Chapter.json");
                string outputPath = Path.Combine(baseTemplatePath, "Chapter.txt");
                string fileContent = File.ReadAllText(outputPath);
                StringBuilder result = new StringBuilder();
                result.AppendLine(fileContent);
                result.Append(characterKey); // 你的额外内容
                prompt = result.ToString();
                Console.WriteLine(result.ToString());
                try
                {
                    if (!string.IsNullOrEmpty(prompt))
                    {

                        Console.WriteLine($"正在生成角色关键词：...");
                        chatHistory.Clear(); // 清空历史
                        chatHistory.AddSystemMessage(sysytemStr); // 重新添加系统提示
                        outstr = await ChatWithAI(prompt, "Character", true, item.ifMarkdown, item.ifJson);
                        await LiteraryFiguresTemplateAsync();
                    }
                    else
                    {
                        Console.WriteLine("\n------生成角色关键词 失败 ------");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"生成角色关键词失败：{ex.Message}");
                }
            }

            return outstr;
        }
        /// <summary>
        /// 根据游戏的世界观和游戏剧情 生成场景关键字
        /// </summary>
        /// <param name="chatService"></param>
        /// <returns></returns>
        public async Task<string> GenerateScences()
        {
            string outstr = "";
            string prompt = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10006");
            if (item != null)
            {
                scenceJsonKeys = item.jsonModle;
                string pattern = @"\{\d+_Output(?:_[^\}]+)?\}\n?";
                string scenceKey = Regex.Replace(item.generateTemplate, pattern, "");
                string sysytemStr = item.systemPrompt;

                //string outputPath = Path.Combine(baseTemplatePath, "Chapter.json");
                string outputPath = Path.Combine(baseTemplatePath, "Chapter.txt");
                string fileContent = File.ReadAllText(outputPath);
                StringBuilder result = new StringBuilder();
                result.AppendLine(fileContent);

                string outputPath1 = Path.Combine(baseTemplatePath, "GameIntroduce.json");
                string jsonString = File.ReadAllText(outputPath1);
                GameItroduce game = JsonConvert.DeserializeObject<GameItroduce>(jsonString);
                result.AppendLine($"游戏简介：{game.game_synopsis}");
                result.Append(scenceKey); // 你的额外内容
                Console.WriteLine(result.ToString());
                prompt = result.ToString();

                try
                {
                    if (!string.IsNullOrEmpty(prompt))
                    {
                        Console.WriteLine($"正在生成场景关键词：...");
                        chatHistory.Clear(); // 清空历史
                        chatHistory.AddSystemMessage(sysytemStr); // 重新添加系统提示
                        outstr = await ChatWithAI(prompt, "Scence", true, item.ifMarkdown, item.ifJson);

                        ArtAIChat_Output artitem = ArtAIChat_Output.getDataById("10002");
                        if (artitem != null)
                        {
                            scencekeysJsonKeys = artitem.jsonModle;
                            await LiteraryScenceTemplateAsync();
                        }
                    }
                    else
                    {
                        Console.WriteLine("\n------生成场景关键词 失败 ------");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"生成场景关键词失败：{ex.Message}");
                }
            }
            return outstr;
        }
        /// <summary>
        /// 根据世界观，游戏剧情和游戏场景  生成背景音乐关键词
        /// </summary>
        /// <param name="chatService"></param>
        /// <returns></returns>
        public async Task<string> GenerateScenceMusic()
        {
            string outstr = "";
            string prompt = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10007");
            if (item != null)
            {
                scencemusicJsonKeys = item.jsonModle;
                string pattern = @"^(世界观|游戏剧情|游戏场景)：\{.*?\}\s*$[\r\n]*";
                // 使用 RegexOptions.Multiline 让 ^ 和 $ 匹配每行的开头和结尾
                string str = Regex.Replace(item.generateTemplate, pattern, string.Empty, RegexOptions.Multiline);

                // 移除可能残留的空行
                str = Regex.Replace(str, @"^\s*$\n", string.Empty, RegexOptions.Multiline);
                string musicKeys = str.Trim();
                string sysytemStr = item.systemPrompt;

                StringBuilder result = new StringBuilder();
                GameBase game = GetGameBaseData();

                result.AppendLine($"世界观：{game.worldview}");
                result.AppendLine($"游戏剧情：{game.story}");

                string outputPath = Path.Combine(baseTemplatePath, "Scence.json");
                string fileContent = File.ReadAllText(outputPath);
                ScenceData scencedata = JsonConvert.DeserializeObject<ScenceData>(fileContent);
                List<Scence> scenceList = scencedata.scenes;
                for (int i = 0; i < scenceList.Count; i++)
                {
                    Scence data = scenceList[i];
                    string scenceDescribtion = $"\n场景{i + 1}:{data.name}\n{data.theme}\n{data.description}";
                    result.AppendLine(scenceDescribtion);
                }
                result.Append(musicKeys); // 你的额外内容
                prompt = result.ToString();
                Console.WriteLine(result.ToString());
                try
                {
                    if (!string.IsNullOrEmpty(prompt))
                    {

                        Console.WriteLine($"正在生成背景音乐关键词：...");
                        chatHistory.Clear(); // 清空历史
                        chatHistory.AddSystemMessage(sysytemStr); // 重新添加系统提示
                        outstr = await ChatWithAI(prompt, "ScenceMusic", true, item.ifMarkdown, item.ifJson);
                    }
                    else
                    {
                        Console.WriteLine("\n------生成背景音乐关键词 失败 ------");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"生成背景音乐关键词失败：{ex.Message}");
                }
            }

            return outstr;
        }
        /// <summary>
        /// 根据世界观 游戏剧情 游戏场景 生成音效关键词
        /// </summary>
        /// <param name="chatService"></param>
        /// <returns></returns>
        public async Task<string> GenerateEffects()
        {
            string outstr = "";
            string prompt = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10008");
            if (item != null)
            {
                scenceeffectsJsonKeys = item.jsonModle;
                //string pattern = @"\{\d+_Output(?:_[^\}]+)?\}\n?";
                //string effectsKeys = Regex.Replace(item.generateTemplate, pattern, "");
                string pattern = @"^(世界观|游戏剧情|游戏场景)：\{.*?\}\s*$[\r\n]*";
                // 使用 RegexOptions.Multiline 让 ^ 和 $ 匹配每行的开头和结尾
                string str = Regex.Replace(item.generateTemplate, pattern, string.Empty, RegexOptions.Multiline);

                // 移除可能残留的空行
                str = Regex.Replace(str, @"^\s*$\n", string.Empty, RegexOptions.Multiline);
                string effectsKeys = str.Trim(); ; //Regex.Replace(item.generateTemplate, @"{.*?_Output_.*?}\n?", "");
                string sysytemStr = item.systemPrompt;

                StringBuilder result = new StringBuilder();
                GameBase game = GetGameBaseData();
                result.AppendLine($"世界观：{game.worldview}");
                result.AppendLine($"游戏剧情：{game.story}");

                string outputPath = Path.Combine(baseTemplatePath, "Scence.json");
                string fileContent = File.ReadAllText(outputPath);
                ScenceData scencedata = JsonConvert.DeserializeObject<ScenceData>(fileContent);
                List<Scence> scenceList = scencedata.scenes;
                for (int i = 0; i < scenceList.Count; i++)
                {
                    Scence data = scenceList[i];
                    string scenceDescribtion = $"\n场景{i + 1}:{data.name}\n{data.theme}\n{data.description}";
                    result.AppendLine(scenceDescribtion);
                }
                result.Append(effectsKeys); // 你的额外内容
                prompt = result.ToString();
                Console.WriteLine(result.ToString());
                try
                {
                    if (!string.IsNullOrEmpty(prompt))
                    {

                        Console.WriteLine($"正在生成音效关键词：...");
                        chatHistory.Clear(); // 清空历史
                        chatHistory.AddSystemMessage(sysytemStr); // 重新添加系统提示
                        outstr = await ChatWithAI(prompt, "ScenceEffects", true, item.ifMarkdown, item.ifJson);
                    }
                    else
                    {
                        Console.WriteLine("\n------生成音效关键词 失败 ------");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"生成音效关键词失败：{ex.Message}");
                }
            }

            return outstr;
        }
        /// <summary>
        /// 文生天空和提示词
        /// </summary>
        /// <returns></returns>
        public async Task<string> GenerateSkyboxTemplate()
        {
            string outstr = "";
            string prompt = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10010");
            if (item != null)
            {
                scenceskyboxJsonKeys = item.jsonModle;
                string pattern = @"\{\d+_Output(?:_[^\}]+)?\}\n?";
                string skyboxksys = Regex.Replace(item.generateTemplate, pattern, "");

                StringBuilder result = new StringBuilder();
                string outputPath = Path.Combine(baseTemplatePath, "Scence.json");
                string fileContent = File.ReadAllText(outputPath);
                ScenceData scencedata = JsonConvert.DeserializeObject<ScenceData>(fileContent);
                List<Scence> scenceList = scencedata.scenes;
                for (int i = 0; i < scenceList.Count; i++)
                {
                    Scence data = scenceList[i];
                    string scenceDescribtion = $"\n场景{i + 1}:{data.name}\n{data.theme}\n{data.description}";
                    result.AppendLine(scenceDescribtion);
                }
                result.Append(skyboxksys); // 你的额外内容
                prompt = result.ToString();
                Console.WriteLine(result.ToString());
                try
                {
                    if (!string.IsNullOrEmpty(prompt))
                    {

                        Console.WriteLine($"正在生成场景天空盒关键词：...");
                        chatHistory.Clear(); // 清空历史
                        outstr = await ChatWithAI(prompt, "ScenceSkyBox", true, item.ifMarkdown, item.ifJson);
                        await LiterarySkyboxTemplateAsync();
                    }
                    else
                    {
                        Console.WriteLine("\n------生成场景天空盒关键词 失败 ------");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"生成场景天空盒关键词失败：{ex.Message}");
                }
            }

            return outstr;
        }

        /// <summary>
        /// 文生人物模板
        /// </summary>
        /// <returns></returns>
        public async Task LiteraryFiguresTemplateAsync()
        {
            //string outputPath = Path.Combine(baseTemplatePath, "Character.json");
            //string fileContent = File.ReadAllText(outputPath);
            //var json = JObject.Parse(fileContent);
            //List<CharacterData> characters = json["characters"].ToObject<List<CharacterData>>();
            //for (int i = 0; i < characters.Count; i++) {
            //    CharacterData data= characters[i];
            //    if (data.id == "角色1")
            //    { //主角
            //        if (data.gender == "none")
            //        {
            //            data.gender = "boy";
            //        }
            //    }
            //}

            string outputPath = Path.Combine(baseTemplatePath, "Character.txt");
            filePath = Path.GetFullPath(outputPath);
            List<Character> characterList = ParseCharacterFile(filePath);
            List<string> characterTemplateList = new List<string>();
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < characterList.Count; i++)
            {
                Character characterData = characterList[i];
                if (characterData.Name == "角色1")
                { //主角
                    if (characterData.Gender == "none")
                    {
                        characterData.Gender = "boy";
                    }
                }
                string templateStr = $"{charactertemplateKeys1},{characterData.Race},{characterData.Gender},{characterData.AppearanceAge},{characterData.Appearance},{characterData.Clothing},{charactertemplateKeys2}";
                string output = templateStr.Replace("none,", "").Replace(",无", "").Replace("none", "").Replace("无", "");
                Console.WriteLine($"文生人物模板 ：{output}");
                characterTemplateList.Add(output);
                result.AppendLine(output);
            }

            if (!Directory.Exists(baseTemplatePath))
                Directory.CreateDirectory(baseTemplatePath);

            string GameIntroducePath = Path.Combine(baseTemplatePath, "CharacterKeys.txt");
            await File.WriteAllTextAsync(GameIntroducePath, result.ToString());
        }

        /// <summary>
        /// 文生场景模板
        /// </summary>
        /// <returns></returns>
        public async Task LiteraryScenceTemplateAsync()
        {
            try
            {
                string outputPath = Path.Combine(baseTemplatePath, "Scence.json");
                string jsonString = File.ReadAllText(outputPath);
                ScenceData game = JsonConvert.DeserializeObject<ScenceData>(jsonString);
                List<Scence> scences = game.scenes;
                Console.WriteLine("文件内容：\n" + jsonString);
                Console.WriteLine($"正式匹配数：{scences.Count}");
                StringBuilder result = new StringBuilder();
                for (int i = 0; i < scences.Count; i++)
                {
                    Scence data = scences[i];
                    string theme = data.theme;
                    string themeEng = theme.Split("|")[1];
                    string output = $"{scencetemplateKeys2},{themeEng},{data.english}";
                    result.AppendLine(output);
                }
                await GenerateGameBaseJson(result.ToString(), "ScenceKeys");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成文生场景模板失败 ：{ex.Message}");
            }
        }
        /// <summary>
        /// 文生天空盒模板
        /// </summary>
        /// <returns></returns>
        public async Task LiterarySkyboxTemplateAsync()
        {
            string outputPath = Path.Combine(baseTemplatePath, "ScenceSkyBox.json");
            string fileContent = File.ReadAllText(outputPath);
            ScenceSkyBox scenceskybox = JsonConvert.DeserializeObject<ScenceSkyBox>(fileContent);
            List<SkyBox> skyboxList = scenceskybox.skybox_prompts;
            try
            {
                StringBuilder result = new StringBuilder();
                for (int i = 0; i < skyboxList.Count; i++)
                {
                    SkyBox skyBox = skyboxList[i];
                    string output = $"{skyboxtemplateKeys1}{skyBox.description_en}{skyboxtemplateKeys2}";
                    result.AppendLine(output);
                }
                if (!Directory.Exists(baseTemplatePath))
                    Directory.CreateDirectory(baseTemplatePath);

                string GameIntroducePath = Path.Combine(baseTemplatePath, "ScenceSkyBoxKeys.txt");
                await File.WriteAllTextAsync(GameIntroducePath, result.ToString());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理失败：{ex.Message}");
            }
        }
        private List<Character> ParseCharacterFile(string filePath)
        {
            var characters = new List<Character>();
            string[] lines = File.ReadAllLines(filePath);

            Character currentCharacter = null;
            foreach (string line in lines)
            {
                if (line.StartsWith("角色"))
                {
                    if (currentCharacter != null)
                    {
                        characters.Add(currentCharacter);
                    }
                    currentCharacter = new Character { Name = line.Split('：')[0].Trim() };
                }
                else if (currentCharacter != null)
                {
                    if (line.Contains("——"))
                    {
                        string[] parts = line.Split(new[] { "——" }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length == 2)
                        {
                            string key = parts[0].Trim();
                            string value = parts[1].Trim();

                            switch (key)
                            {
                                case "种族":
                                    currentCharacter.Race = ExtractEnglishValue(value);
                                    break;
                                case "性别":
                                    currentCharacter.Gender = ExtractEnglishValue(value);
                                    break;
                                case "外貌年龄":
                                    currentCharacter.AppearanceAge = ExtractEnglishValue(value);
                                    break;
                                case "外貌":
                                    currentCharacter.Appearance = ExtractEnglishValue(value);
                                    break;
                                case "穿着":
                                    currentCharacter.Clothing = ExtractEnglishValue(value);
                                    break;
                            }
                        }
                    }
                }
            }

            if (currentCharacter != null)
            {
                characters.Add(currentCharacter);
            }

            return characters;
        }
        private Dictionary<string, string> designCaseDic = new Dictionary<string, string>();
        /// <summary>
        /// 生成 多个策划案
        /// </summary>
        /// <returns></returns>
        public async Task GenerateDesignCases()
        {
            GameBase game = GetGameBaseData();
            designCaseDic.Clear();
            List<DesignAIChat_Output> list = DesignAIChat_Output.getAllDatasList();
            for (int i = 0; i < list.Count; i++)
            {
                string prompt = "";
                DesignAIChat_Output data = list[i];
                if (!string.IsNullOrEmpty(data.systemPrompt))
                {

                    prompt = Regex.Replace(data.generateTemplate, @"\{10001_Output_游戏名\}", game.gamename);
                    prompt = Regex.Replace(prompt, @"\{10001_Output_题材\}", game.theme);
                    prompt = Regex.Replace(prompt, @"\{10001_Output_游戏类型\}", game.gametype);
                    prompt = Regex.Replace(prompt, @"\{10001_Output_游戏视角\}", game.perspective);
                    prompt = Regex.Replace(prompt, @"\{10001_Output_3D或2D\}", game.dimension);
                    prompt = Regex.Replace(prompt, @"\{10001_Output_世界观\}", game.worldview);
                    prompt = Regex.Replace(prompt, @"\{10001_Output_游戏剧情\}", game.story);
                    if (int.TryParse(data.id, out int intValue) && Enum.IsDefined(typeof(DesignType), intValue))
                    {
                        DesignType fromNumber = (DesignType)intValue;
                        //Console.WriteLine($"从数值转换: {fromNumber}");
                        switch (fromNumber)
                        {
                            case DesignType.coreGameplay:
                                designCaseDic.Add("coreGameplay", prompt);
                                break;
                            case DesignType.userInterface:
                                designCaseDic.Add("userInterface", prompt);
                                break;
                            case DesignType.friend:
                                designCaseDic.Add("friend", prompt);
                                break;
                            case DesignType.tradeUnion:
                                designCaseDic.Add("tradeUnion", prompt);
                                break;
                            case DesignType.currency:
                                designCaseDic.Add("currency", prompt);
                                break;
                            case DesignType.shop:
                                designCaseDic.Add("shop", prompt);
                                break;
                            case DesignType.payment:
                                designCaseDic.Add("payment", prompt);
                                break;
                            case DesignType.task:
                                designCaseDic.Add("task", prompt);
                                break;
                            case DesignType.newguide:
                                designCaseDic.Add("newguide", prompt);
                                break;
                            case DesignType.activity:
                                designCaseDic.Add("activity", prompt);
                                break;
                            case DesignType.setting:
                                designCaseDic.Add("setting", prompt);
                                break;
                        }
                    }
                }
            }
            await GetCaseDicFirstItem();
        }
        private async Task GetCaseDicFirstItem()
        {
            if (designCaseDic.Count > 0)
            {
                // 获取第一个键值对
                var firstPair = designCaseDic.First();
                string firstKey = firstPair.Key;
                string firstValue = firstPair.Value;

                Console.WriteLine($"取出的元素: Key = {firstKey}, Value = {firstValue}");
                await GenerateCase(ChatService, firstKey, firstValue);
                Console.WriteLine($"移除后字典中的元素数量: {designCaseDic.Count}");
            }
            else
            {
                Console.WriteLine("字典为空，无法取出元素。");
            }
        }
        private async Task GenerateCase(IChatCompletionService chatService, string filename, string prompt)
        {
            chatHistory.Clear();
            chatHistory.AddUserMessage(prompt); // 添加用户输入到历史
            var generatedContent = new StringBuilder();
            await foreach (var chunk in chatService.GetStreamingChatMessageContentsAsync(chatHistory))
            {
                Console.Write(chunk.Content);
                generatedContent.Append(chunk.Content);
            }
            if (!Directory.Exists(baseTemplatePath))
                Directory.CreateDirectory(baseTemplatePath);

            string outputPath = Path.Combine(baseTemplatePath, $"{filename}.md");
            await File.WriteAllTextAsync(outputPath, generatedContent.ToString());
            // 从字典中移除
            designCaseDic.Remove(filename);
            await GetCaseDicFirstItem();
            Console.WriteLine("\n------ 生成 filename 完成 ------");
        }


        string ExtractEnglishValue(string input)
        {
            // 同时匹配中文括号（）和英文括号()
            var match = Regex.Match(input, @"[（(]([^）)]+)[）)]");
            if (match.Success)
            {
                string outStr = "";
                if (match.Groups[1].Value != "Unknown")
                {
                    outStr = match.Groups[1].Value;
                }
                return outStr;
            }
            return input;
        }

        public async Task<string> ChatWithAI(string prompt, string filename, bool outPutJson, bool generateMDbol, bool generateJsonBol)
        {
            string outstr = "";
            string mdStr = "";
            string jsonStr = "";
            chatHistory.AddUserMessage(prompt); // 添加用户输入到历史
            var generatedContent = new StringBuilder();
            await foreach (var chunk in ChatService.GetStreamingChatMessageContentsAsync(chatHistory))
            {
                Console.Write(chunk.Content);
                generatedContent.Append(chunk.Content);
            }
            outstr = generatedContent.ToString();
            // AI 的回复会自动添加到 chatHistory 中，后续对话可以基于此继续

            if (!Directory.Exists(baseTemplatePath))
                Directory.CreateDirectory(baseTemplatePath);

            string outputPath = Path.Combine(baseTemplatePath, $"{filename}.txt");
            await File.WriteAllTextAsync(outputPath, generatedContent.ToString());
            Console.WriteLine($"\n------ 生成 {filename} .txt完成 ------");
            if (generateMDbol == true)
            {
                mdStr = await GenerateMDfile(generatedContent.ToString(), filename);
                Console.WriteLine($"\n------ 生成 {filename} .md完成 ------");
            }
            if (generateJsonBol == true)
            {
                jsonStr = await GenerateGameBaseJson(generatedContent.ToString(), filename);
                Console.WriteLine($"\n------ 生成 {filename} .json完成 ------");
            }
            if (outPutJson == true)
            {
                return jsonStr;
            }
            else
            {
                return mdStr;
            }
        }
        /// <summary>
        /// 生成.md文件
        /// </summary>
        /// <param name="chatService"></param>
        /// <param name="prompt"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public async Task<string> GenerateMDfile(string prompt, string filename)
        {
            string mdStr = "";
            AI_Game_Output item = AI_Game_Output.getDataById("10011");
            if (item != null)
            {
                string markdownKeys = item.generateTemplate;
                string promptStr = prompt + $"\r\n{markdownKeys}";
                Console.WriteLine($"markdown KEYS : {promptStr}");
                chatHistory.Clear();
                chatHistory.AddUserMessage(promptStr); // 添加用户输入到历史
                var generatedContent = new StringBuilder();
                await foreach (var chunk in ChatService.GetStreamingChatMessageContentsAsync(chatHistory))
                {
                    Console.Write(chunk.Content);
                    generatedContent.Append(chunk.Content);
                }
                if (!Directory.Exists(baseTemplatePath))
                    Directory.CreateDirectory(baseTemplatePath);

                mdStr = RemoveMarkdownCodeBlocks(generatedContent.ToString());
                string outputPath = Path.Combine(baseTemplatePath, $"{filename}.md");
                await File.WriteAllTextAsync(outputPath, mdStr.ToString());
            }
            return mdStr;
        }
        /// <summary>
        /// 去除 ```markdown  ```
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        string RemoveMarkdownCodeBlocks(string input)
        {
            // 移除开头的```markdown
            int startIndex = input.IndexOf("```markdown");
            if (startIndex >= 0)
            {
                input = input.Substring(startIndex + "```markdown".Length);
            }

            // 移除结尾的```
            int endIndex = input.LastIndexOf("```");
            if (endIndex >= 0)
            {
                input = input.Substring(0, endIndex);
            }

            return input.Trim();
        }

        private async Task<string> GenerateGameBaseJson(string prompt, string filename)
        {
            string promptStr = "";
            switch (filename)
            {
                case "GameBase":
                    promptStr = prompt + $"\r\n{gameBaseJsonKeys}";
                    break;
                case "IGNreviewlink":
                    promptStr = prompt + $"\r\n{ignlinkJsonKeys}";
                    break;
                case "GameIntroduce":
                    promptStr = prompt + $"\r\n{gameintroduceKeys}";
                    break;
                case "Chapter":
                    promptStr = prompt + $"\r\n{chapterJsonKeys}";
                    break;
                case "Character":
                    promptStr = prompt + $"\r\n{characterJsonKeys}";
                    break;
                case "Scence":
                    promptStr = prompt + $"\r\n{scenceJsonKeys}";
                    break;
                case "ScenceKeys":
                    promptStr = prompt + $"\r\n{scencekeysJsonKeys}";
                    break;
                case "ScenceMusic":
                    promptStr = prompt + $"\r\n{scencemusicJsonKeys}";
                    break;
                case "ScenceEffects":
                    promptStr = prompt + $"\r\n{scenceeffectsJsonKeys}";
                    break;
                case "ScenceSkyBox":
                    promptStr = prompt + $"\r\n{scenceskyboxJsonKeys}";
                    break;
            }

            chatHistory.Clear();
            chatHistory.AddUserMessage(promptStr); // 添加用户输入到历史
            var generatedContent = new StringBuilder();
            await foreach (var chunk in ChatService.GetStreamingChatMessageContentsAsync(chatHistory))
            {
                Console.Write(chunk.Content);
                generatedContent.Append(chunk.Content);
            }

            // AI 的回复会自动添加到 chatHistory 中，后续对话可以基于此继续

            if (!Directory.Exists(baseTemplatePath))
                Directory.CreateDirectory(baseTemplatePath);

            string mdStr = RemoveJSONCodeBlocks(generatedContent.ToString());
            string outputPath = Path.Combine(baseTemplatePath, $"{filename}.json");
            await File.WriteAllTextAsync(outputPath, mdStr.ToString());
            return mdStr;
        }
        /// <summary>
        /// 去除 ```json  ```
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        string RemoveJSONCodeBlocks(string input)
        {
            // 移除开头的```markdown
            int startIndex = input.IndexOf("```json");
            if (startIndex >= 0)
            {
                input = input.Substring(startIndex + "```json".Length);
            }

            // 移除结尾的```
            int endIndex = input.LastIndexOf("```");
            if (endIndex >= 0)
            {
                input = input.Substring(0, endIndex);
            }

            return input.Trim();
        }
        private GameBase GetGameBaseData()
        {
            string path = Path.Combine(baseTemplatePath, "GameBase.json");
            string jsonString = File.ReadAllText(path);
            GameBase game = JsonConvert.DeserializeObject<GameBase>(jsonString);
            return game;
        }
    }
    /// <summary>
    /// 策划案的类型
    /// </summary>
    enum DesignType
    {
        coreGameplay = 10001,
        userInterface,
        friend,
        tradeUnion,
        currency,
        shop,
        payment,
        task,
        newguide,
        activity,
        setting
    }

    class Character
    {
        public string Name { get; set; }
        public string Race { get; set; }
        public string Gender { get; set; }
        public string AppearanceAge { get; set; }
        public string Appearance { get; set; }
        public string Clothing { get; set; }
    }
    class GameBase
    {
        //游戏名
        public string gamename { get; set; }
        //题材
        public string theme { get; set; }
        //游戏类型
        public string gametype { get; set; }
        //游戏视角
        public string perspective { get; set; }
        //2d/3d
        public string dimension { get; set; }
        //世界观
        public string worldview { get; set; }
        //游戏剧情
        public string story { get; set; }
        //游戏目标
        public GameTarget objectives { get; set; }
    }
    class GameTarget
    {
        public string short_term { get; set; }
        public string mid_term { get; set; }
        public string long_term { get; set; }
    }
    class GameItroduce
    {
        public string game_synopsis { get; set; }
        public string game_introduction { get; set; }
    }

    class CharacterData
    {
        public string id { get; set; }
        //定位
        public string position { get; set; }
        //名字
        public string name { get; set; }
        //种族
        public string race { get; set; }
        //性别
        public string gender { get; set; }
        //实际年龄
        public string actual_age { get; set; }
        //外貌年龄
        public string appearance_age { get; set; }
        //外貌
        public string appearance { get; set; }
        //穿着
        public string clothing { get; set; }
        //性格
        public string personality { get; set; }
        //背景
        public string background { get; set; }
        //动机
        public string motivation { get; set; }
    }
    class ScenceData
    {
        public string style { get; set; }
        public string framing { get; set; }
        public string perspective { get; set; }
        public List<Scence> scenes { get; set; }
    }
    class Scence
    {
        public string name { get; set; }
        public string theme { get; set; }
        public string description { get; set; }
        public string english { get; set; }
    }

    class ScenceSkyBox
    {
        public List<SkyBox> skybox_prompts { get; set; }
    }
    class SkyBox
    {
        public string scene { get; set; }
        public string description_cn { get; set; }
        public string description_en { get; set; }
    }
    class ChapterJson
    {
        public string mdData { get; set; }
    }
}
